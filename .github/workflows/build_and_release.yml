---
name: Publish Python 🐍 distributions 📦 to PyPI
on:
  push:
    tags:
      - "*"
jobs:
  extract-tag:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.extract_tag.outputs.tag }}
    steps:
      - uses: actions/checkout@v4
      - id: extract_tag
        name: Extract tag name
        run: echo "::set-output name=tag::$(echo $GITHUB_REF | cut -d / -f 3)"

  build-and-publish-test-pypi:
    needs: extract-tag
    runs-on: ubuntu-latest
    environment:
      name: pypi
    permissions:
      id-token: write # IMPORTANT: mandatory for trusted publishing
      contents: write
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.11
        uses: actions/setup-python@v3
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: python3 -m pip install --upgrade setuptools wheel twine build semver packaging
      - name: Get correct version for TestPyPI release
        id: check_version
        run: |
          VERSION=${{ needs.extract-tag.outputs.version }}  
          PACKAGE_NAME="dspy-ai-test"
          echo "Checking if $VERSION for $PACKAGE_NAME exists on TestPyPI"  
          NEW_VERSION=$(python3 .github/workflows/build_utils/test_version.py $PACKAGE_NAME $VERSION)  
          echo "Version to be used for TestPyPI release: $NEW_VERSION"  
          echo "::set-output name=version::$NEW_VERSION"
      - name: Update version in pyproject.toml
        run: sed -i '/#replace_package_version_marker/{n;s/version="[^"]*"/version="${{ steps.check_version.outputs.version }}"/;}' pyproject.toml
      - name: Update package name in pyproject.toml
        run: sed -i '/#replace_package_name_marker/{n;s/name="[^"]*"/name="dspy-ai-test"/;}' pyproject.toml
      - name: Build a binary wheel
        run: python3 -m build
      # Test the locally built wheel
      - name: Create test environment
        run: python -m venv test_before_testpypi
      - name: Test package installation and functionality
        run: |
          source test_before_testpypi/bin/activate
          # Install the locally built wheel and testing dependencies
          pip install dist/*.whl pytest pytest-asyncio
          pytest tests/metadata/test_metadata.py tests/predict
          deactivate
      # Publish to test-PyPI
      - name: Publish distribution 📦 to test-PyPI
        uses: pypa/gh-action-pypi-publish@release/v1 # This requires a trusted publisher to be setup in pypi/testpypi
        with:
          repository-url: https://test.pypi.org/legacy/

  # TODO: Add tests using dspy-ai-test

  build-and-publish-pypi:
    needs: [extract-tag, build-and-publish-test-pypi]
    # Only publish to PyPI if the repository owner is stanfordnlp
    if: github.repository_owner == 'stanfordnlp'
    runs-on: ubuntu-latest
    environment:
      name: pypi
    permissions:
      id-token: write # IMPORTANT: mandatory for trusted publishing
      contents: write
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.11
        uses: actions/setup-python@v3
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: python3 -m pip install --upgrade setuptools wheel twine build
      - name: Update version in pyproject.toml
        run: sed -i '/#replace_package_version_marker/{n;s/version *= *"[^"]*"/version="${{ needs.extract-tag.outputs.version }}"/;}' pyproject.toml
      - name: Update version in __metadata__.py
        run: sed -i '/#replace_package_version_marker/{n;s/__version__ *= *"[^"]*"/__version__="${{ needs.extract-tag.outputs.version }}"/;}' ./dspy/__metadata__.py
      # Publish to dspy
      - name: Update package name in pyproject.toml
        run: sed -i '/#replace_package_name_marker/{n;s/name *= *"[^"]*"/name="dspy"/;}' pyproject.toml
      - name: Update package name in metadata.py
        run: sed -i '/#replace_package_name_marker/{n;s/__name__ *= *"[^"]*"/__name__="dspy"/;}' ./dspy/__metadata__.py
      - name: Build a binary wheel
        run: python3 -m build
      # Test the locally built wheel before publishing to pypi
      - name: Create test environment
        run: python -m venv test_before_pypi
      - name: Test package installation and functionality
        run: |
          source test_before_pypi/bin/activate
          # Install the locally built wheel and testing dependencies
          pip install dist/*.whl pytest pytest-asyncio
          pytest tests/metadata/test_metadata.py tests/predict
          deactivate
          rm -r test_before_pypi
      - name: Publish distribution 📦 to PyPI (dspy)
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          attestations: false
      # Publish to dspy-ai
      - name: Update package name in pyproject.toml
        run: sed -i '/#replace_package_name_marker/{n;s/name *= *"[^"]*"/name="dspy-ai"/;}' .github/.internal_dspyai/pyproject.toml
      - name: Update version for dspy-ai release
        run: sed -i '/#replace_package_version_marker/{n;s/version *= *"[^"]*"/version="${{ needs.extract-tag.outputs.version }}"/;}' .github/.internal_dspyai/pyproject.toml
      - name: Update dspy dependency for dspy-ai release
        run: |
          sed -i '/#replace_dspy_version_marker/{n;s/dspy>=[^"]*/dspy>=${{ needs.extract-tag.outputs.version }}/;}' .github/.internal_dspyai/pyproject.toml
      - name: Build a binary wheel (dspy-ai)
        run: python3 -m build .github/.internal_dspyai/
      - name: Publish distribution 📦 to PyPI (dspy-ai)
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          attestations: false
          packages-dir: .github/.internal_dspyai/dist/
      - uses: stefanzweifel/git-auto-commit-action@v5 # auto commit changes to main
        with:
          commit_message: Update versions
          create_branch: true
          branch: release-${{ needs.extract-tag.outputs.version }}
      - name: Checkout main branch
        run: |
          git fetch origin
          git checkout main
      - name: Configure git user
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Github Actions"
      - name: Merge release branch into main
        run: |
          git merge --no-ff release-${{ needs.extract-tag.outputs.version }}
      - name: Push changes to main
        run: |
          git push origin main
