name: Pre-commit checks
on:
  workflow_dispatch:

jobs:
  pre-commit-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"
          cache: "pip"
      - name: Check Pull Request Title
        uses: Slashgear/action-check-pr-title@main
        with:
          regexp: '(break|build|ci|docs|feat|fix|perf|refactor|style|test|ops|hotfix|release|maint|init|enh|revert)\([a-z,A-Z,0-9,\-,\_,\/,:]+\)(:)\s{1}([\w\s]+)' # Regex the title should match.
      - name: Getting changed files list
        id: files
        uses: jitterbit/get-changed-files@master
      - name: Checking changed files
        shell: bash
        run: |
          echo "Changed files"
          echo ${{ steps.files.outputs.all }}
          echo "GitHub Client version"
          echo $(gh --version)
      - name: Pre-Commit Checks
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit
          echo "Running pre-commit scans:"
          # adding log display in case of pre-commit errors
          pre-commit run -v --files ${{ steps.files.outputs.all }}
        shell: bash
