name: Lin<PERSON>, <PERSON>, and Build

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  fix:
    name: <PERSON> Ruff Fix
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install uv with caching
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: |
            **/pyproject.toml
            **/uv.lock
      - name: Create and activate virtual environment
        run: |
          uv venv .venv
          echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH
      - name: Install dependencies
        run: uv sync --dev -p .venv --extra dev
      - name: Ruff Check
        run: |
          ruff check --fix-only --diff --exit-non-zero-on-fix || (
            echo ""
            echo "❌ Ruff found issues that can be fixed automatically."
            echo "💡 To fix them locally, run:"
            echo ""
            echo "    pre-commit run --all-files"
            echo ""
            echo "Then commit and push the changes."
            exit 1
          )

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12", "3.13"]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install Deno
        run: |
          curl -fsSL https://deno.land/install.sh | sh
          echo "${HOME}/.deno/bin" >> $GITHUB_PATH
      - name: Verify Deno installation
        run: deno --version
      - name: Install uv with caching
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: |
            **/pyproject.toml
            **/uv.lock
      - name: Create and activate virtual environment
        run: |
          uv venv .venv
          echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH
      - name: Install dependencies
        run: |
          uv sync --dev -p .venv --extra dev
          uv pip list
      - name: Run lint with tests
        uses: chartboost/ruff-action@v1
        with:
          args: check --fix-only
      - name: Run tests with pytest
        run: uv run -p .venv pytest -vv tests/
      - name: Install optional dependencies
        run: uv sync -p .venv --extra dev --extra test_extras
      - name: Run extra tests
        run: uv run -p .venv pytest tests/ -m extra --extra
  
  llm_call_test:
    name: Run Tests with Real LM
    runs-on: ubuntu-latest
    services:
      ollama:
        image: ollama/ollama:latest
        ports:
          - 11434:11434
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.11
      - name: Install uv with caching
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: |
            **/pyproject.toml
            **/uv.lock
      - name: Create and activate virtual environment
        run: |
          uv venv .venv
          echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH
      - name: Install dependencies
        run: |
          uv sync --dev -p .venv --extra dev
          uv pip list
      - name: Pull LLM
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:11434/api/version; do sleep 2; done'
          curl -X POST http://localhost:11434/api/pull \
            -H "Content-Type: application/json" \
            -d '{"name": "llama3.2:3b"}'
          echo "LM_FOR_TEST=ollama/llama3.2:3b" >> $GITHUB_ENV
      - name: Run tests
        run: uv run -p .venv pytest -m llm_call --llm_call -vv --durations=5 tests/

  build_package:
    name: Build Package
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12", "3.13"]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv with caching
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: |
            **/pyproject.toml
            **/uv.lock
      - name: Create and activate virtual environment
        run: |
          uv venv .venv
          echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH
      - name: Install dependencies
        run: uv sync --dev -p .venv --extra dev
      - name: Build
        run: uv run -p .venv python -m build
      - name: Install built package
        run: uv pip install dist/*.whl -p .venv
      - name: Test import dspy
        run: uv run -p .venv python -c "import dspy"
