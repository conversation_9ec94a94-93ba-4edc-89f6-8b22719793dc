# dspy.JSONAdapter

<!-- START_API_REF -->
::: dspy.JSONAdapter
    handler: python
    options:
        members:
            - __call__
            - acall
            - format
            - format_assistant_message_content
            - format_conversation_history
            - format_demos
            - format_field_description
            - format_field_structure
            - format_field_with_value
            - format_finetune_data
            - format_task_description
            - format_user_message_content
            - parse
            - user_message_output_requirements
        show_source: true
        show_root_heading: true
        heading_level: 2
        docstring_style: google
        show_root_full_path: true
        show_object_full_path: false
        separate_signature: false
        inherited_members: true
:::
<!-- END_API_REF -->
