# dspy.LM

<!-- START_API_REF -->
::: dspy.LM
    handler: python
    options:
        members:
            - __call__
            - acall
            - aforward
            - copy
            - dump_state
            - finetune
            - forward
            - infer_provider
            - inspect_history
            - kill
            - launch
            - reinforce
            - update_global_history
        show_source: true
        show_root_heading: true
        heading_level: 2
        docstring_style: google
        show_root_full_path: true
        show_object_full_path: false
        separate_signature: false
        inherited_members: true
:::
<!-- END_API_REF -->
