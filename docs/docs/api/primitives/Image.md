# dspy.Image

<!-- START_API_REF -->
::: dspy.Image
    handler: python
    options:
        members:
            - description
            - extract_custom_type_from_annotation
            - format
            - from_PIL
            - from_file
            - from_url
            - serialize_model
            - validate_input
        show_source: true
        show_root_heading: true
        heading_level: 2
        docstring_style: google
        show_root_full_path: true
        show_object_full_path: false
        separate_signature: false
        inherited_members: true
:::
<!-- END_API_REF -->
