# dspy.Prediction

<!-- START_API_REF -->
::: dspy.Prediction
    handler: python
    options:
        members:
            - copy
            - from_completions
            - get
            - get_lm_usage
            - inputs
            - items
            - keys
            - labels
            - set_lm_usage
            - toDict
            - values
            - with_inputs
            - without
        show_source: true
        show_root_heading: true
        heading_level: 2
        docstring_style: google
        show_root_full_path: true
        show_object_full_path: false
        separate_signature: false
        inherited_members: true
:::
<!-- END_API_REF -->
