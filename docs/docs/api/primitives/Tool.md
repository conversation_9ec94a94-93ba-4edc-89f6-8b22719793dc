# dspy.Tool

<!-- START_API_REF -->
::: dspy.Tool
    handler: python
    options:
        members:
            - __call__
            - acall
            - description
            - extract_custom_type_from_annotation
            - format
            - format_as_litellm_function_call
            - from_langchain
            - from_mcp_tool
            - serialize_model
        show_source: true
        show_root_heading: true
        heading_level: 2
        docstring_style: google
        show_root_full_path: true
        show_object_full_path: false
        separate_signature: false
        inherited_members: true
:::
<!-- END_API_REF -->
