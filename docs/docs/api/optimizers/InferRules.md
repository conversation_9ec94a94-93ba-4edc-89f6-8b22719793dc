# dspy.InferRules

<!-- START_API_REF -->
::: dspy.InferRules
    handler: python
    options:
        members:
            - compile
            - evaluate_program
            - format_examples
            - get_params
            - get_predictor_demos
            - induce_natural_language_rules
            - update_program_instructions
        show_source: true
        show_root_heading: true
        heading_level: 2
        docstring_style: google
        show_root_full_path: true
        show_object_full_path: false
        separate_signature: false
        inherited_members: true
:::
<!-- END_API_REF -->
