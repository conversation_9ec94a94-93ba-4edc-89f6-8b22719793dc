# dspy.streaming.StatusMessageProvider

<!-- START_API_REF -->
::: dspy.streaming.StatusMessageProvider
    handler: python
    options:
        members:
            - lm_end_status_message
            - lm_start_status_message
            - module_end_status_message
            - module_start_status_message
            - tool_end_status_message
            - tool_start_status_message
        show_source: true
        show_root_heading: true
        heading_level: 2
        docstring_style: google
        show_root_full_path: true
        show_object_full_path: false
        separate_signature: false
        inherited_members: true
:::
<!-- END_API_REF -->
