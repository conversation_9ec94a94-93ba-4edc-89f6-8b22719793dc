# Using DSPy in Production

<div class="grid cards" style="text-align: left;" markdown>

- :material-earth:{ .lg .middle } __Real-World Use Cases__

    ---

    DSPy is deployed in production by many enterprises and startups. Explore real-world case studies.

    [:octicons-arrow-right-24: Use Cases](../community/use-cases.md)

- :material-magnify-expand:{ .lg .middle } __Monitoring & Observability__

    ---

    Monitor your DSPy programs using **MLflow Tracing**, based on OpenTelemetry.

    [:octicons-arrow-right-24: Set Up Observability](../tutorials/observability/index.md#tracing)

- :material-ab-testing: __Reproducibility__

    ---

    Log programs, metrics, configs, and environments for full reproducibility with DSPy's native MLflow integration.

    [:octicons-arrow-right-24: MLflow Integration](https://mlflow.org/docs/latest/llms/dspy/index.html)

- :material-rocket-launch: __Deployment__

    ---

    When it's time to productionize, deploy your application easily with DSPy's integration with MLflow Model Serving.

    [:octicons-arrow-right-24: Deployment Guide](../tutorials/deployment/index.md)

- :material-arrow-up-right-bold: __Scalability__

    ---

    DSPy is designed with thread-safety in mind and offers native asynchronous execution support for high-throughput environments.

    [:octicons-arrow-right-24: Async Program](../api/utils/asyncify.md)

- :material-alert-rhombus: __Guardrails & Controllability__

    ---

    DSPy's **Signatures**, **Modules**, and **Optimizers** help you control and guide LM outputs.

    [:octicons-arrow-right-24: Learn Signature](../learn/programming/signatures.md)

</div>
