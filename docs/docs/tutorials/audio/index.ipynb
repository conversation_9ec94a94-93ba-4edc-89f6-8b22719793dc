{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tutorial: Using Audio in DSPy Programs\n", "\n", "This tutorial walks through building pipelines for audio-based applications using DSPy."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Install Dependencies\n", "\n", "Ensure you're using the latest DSPy version:\n", "\n", "```shell\n", "pip install -U dspy\n", "```\n", "\n", "To handle audio data, install the following dependencies:\n", "\n", "```shell\n", "pip install datasets soundfile torch==2.0.1+cu118 torchaudio==2.0.2+cu118\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load the Spoken-SQuAD Dataset\n", "\n", "We'll use the Spoken-SQuAD dataset ([Official](https://github.com/<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>/Spoken-SQuAD) & [HuggingFace version](https://huggingface.co/datasets/AudioLLMs/spoken_squad_test) for tutorial demonstration), which contains spoken audio passages used for question-answering:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import dspy\n", "from dspy.datasets import DataLoader\n", "\n", "kwargs = dict(fields=(\"context\", \"instruction\", \"answer\"), input_keys=(\"context\", \"instruction\"))\n", "spoken_squad = DataLoader().from_huggingface(dataset_name=\"AudioLLMs/spoken_squad_test\", split=\"train\", trust_remote_code=True, **kwargs)\n", "\n", "random.Random(42).shuffle(spoken_squad)\n", "spoken_squad = spoken_squad[:100]\n", "\n", "split_idx = len(spoken_squad) // 2\n", "trainset_raw, testset_raw = spoken_squad[:split_idx], spoken_squad[split_idx:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Preprocess Audio Data\n", "\n", "The audio clips in the dataset require some preprocessing into byte arrays with their corresponding sampling rates."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocess(x):\n", "    audio = dspy.Audio.from_array(x.context[\"array\"], x.context[\"sampling_rate\"])\n", "    return dspy.Example(\n", "        passage_audio=audio,\n", "        question=x.instruction,\n", "        answer=x.answer\n", "    ).with_inputs(\"passage_audio\", \"question\")\n", "\n", "trainset = [preprocess(x) for x in trainset_raw]\n", "testset = [preprocess(x) for x in testset_raw]\n", "\n", "len(trainset), len(testset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DSPy program for spoken question answering\n", "\n", "Let's define a simple DSPy program that uses audio inputs to answer questions directly. This is very similar to the [BasicQA](https://dspy.ai/cheatsheet/?h=basicqa#dspysignature) task, with the only difference being that the passage context is provided as an audio file for the model to listen to and answer the question:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SpokenQASignature(dspy.Signature):\n", "    \"\"\"Answer the question based on the audio clip.\"\"\"\n", "    passage_audio: dspy.Audio = dspy.InputField()\n", "    question: str = dspy.InputField()\n", "    answer: str = dspy.OutputField(desc = 'factoid answer between 1 and 5 words')\n", "\n", "spoken_qa = dspy.ChainOfThought(SpokenQASignature)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's configure our LLM which can process input audio. \n", "\n", "```python\n", "dspy.settings.configure(lm=dspy.LM(model='gpt-4o-mini-audio-preview-2024-12-17'))\n", "```\n", "\n", "Note: Using `dspy.Audio` in signatures allows passing in audio directly to the model. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define Evaluation Metric\n", "\n", "We'll use the Exact Match metric (`dspy.evaluate.answer_exact_match`) to measure answer accuracy compared to the provided reference answers:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["evaluate_program = dspy.Evaluate(devset=testset, metric=dspy.evaluate.answer_exact_match,display_progress=True, num_threads = 10, display_table=True)\n", "\n", "evaluate_program(spoken_qa)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optimize with DSPy\n", "\n", "You can optimize this audio-based program as you would for any DSPy program using any DSPy optimizer.\n", "\n", "Note: Audio tokens can be costly so it is recommended to configure optimizers like `dspy.BootstrapFewShotWithRandomSearch` or `dspy.MIPROv2` conservatively with 0-2 few shot examples and less candidates / trials than the optimizer default parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["optimizer = dspy.BootstrapFewShotWithRandomSearch(metric = dspy.evaluate.answer_exact_match, max_bootstrapped_demos=2, max_labeled_demos=2, num_candidate_programs=5)\n", "\n", "optimized_program = optimizer.compile(spoken_qa, trainset = trainset)\n", "\n", "evaluate_program(optimized_program)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_lm = dspy.LM(model='gpt-4o-mini') #NOTE - this is the LLM guiding the MIPROv2 instruction candidate proposal\n", "optimizer = dspy.MIPROv2(metric=dspy.evaluate.answer_exact_match, auto=\"light\", prompt_model = prompt_lm)\n", "\n", "#NOTE - MIPROv2's dataset summarizer cannot process the audio files in the dataset, so we turn off the data_aware_proposer \n", "optimized_program = optimizer.compile(spoken_qa, trainset=trainset, max_bootstrapped_demos=2, max_labeled_demos=2, data_aware_proposer=False)\n", "\n", "evaluate_program(optimized_program)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With this small subset, MIPROv2 led to a ~10% improvement over baseline performance."]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we’ve seen how to use an audio-input-capable LLM in DSPy, let’s flip the setup.\n", "\n", "In this next task, we'll use a standard text-based LLM to generate prompts for a text-to-speech model and then evaluate the quality of the produced speech for some downstream task. This approach is generally more cost-effective than asking an LLM like `gpt-4o-mini-audio-preview-2024-12-17` to generate audio directly, while still enabling a pipeline that can be optimized for higher-quality speech output."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load the CREMA-D Dataset\n", "\n", "We'll use the CREMA-D dataset ([Official](https://github.com/CheyneyComputerScience/CREMA-D) & [HuggingFace version](https://huggingface.co/datasets/myleslinder/crema-d) for tutorial demonstration), which includes audio clips of chosen participants speaking the same line with one of six target emotions: neutral, happy, sad, anger, fear, and disgust."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "label_map = ['neutral', 'happy', 'sad', 'anger', 'fear', 'disgust']\n", "\n", "kwargs = dict(fields=(\"sentence\", \"label\", \"audio\"), input_keys=(\"sentence\", \"label\"))\n", "crema_d = DataLoader().from_huggingface(dataset_name=\"myleslinder/crema-d\", split=\"train\", trust_remote_code=True, **kwargs)\n", "\n", "def preprocess(x):\n", "    return dspy.Example(\n", "        raw_line=x.sentence,\n", "        target_style=label_map[x.label],\n", "        reference_audio=dspy.Audio.from_array(x.audio[\"array\"], x.audio[\"sampling_rate\"])\n", "    ).with_inputs(\"raw_line\", \"target_style\")\n", "\n", "random.Random(42).shuffle(crema_d)\n", "crema_d = crema_d[:100]\n", "\n", "random.seed(42)\n", "label_to_indices = defaultdict(list)\n", "for idx, x in enumerate(crema_d):\n", "    label_to_indices[x.label].append(idx)\n", "\n", "per_label = 100 // len(label_map)\n", "train_indices, test_indices = [], []\n", "for indices in label_to_indices.values():\n", "    selected = random.sample(indices, min(per_label, len(indices)))\n", "    split = len(selected) // 2\n", "    train_indices.extend(selected[:split])\n", "    test_indices.extend(selected[split:])\n", "\n", "trainset = [preprocess(crema_d[idx]) for idx in train_indices]\n", "testset = [preprocess(crema_d[idx]) for idx in test_indices]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DSPy pipeline for generating TTS instructions for speaking with a target emotion\n", "\n", "We’ll now build a pipeline that generates emotionally expressive speech by prompting a TTS model with both a line of text and an instruction on how to say it. \n", "The goal of this task will be to use DSPy to generate prompts that guide the TTS output to match the emotion and style of reference audio from the dataset.\n", "\n", "First let’s set up the TTS generator to produce generate spoken audio with a specified emotion or style. \n", "We utilize `gpt-4o-mini-tts` as it supports prompting the model with raw input and speaking and produces an audio response as a `.wav` file processed with `dspy.Audio`. \n", "We also set up a cache for the TTS outputs."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import base64\n", "import hashlib\n", "from openai import OpenAI\n", "\n", "CACHE_DIR = \".audio_cache\"\n", "os.makedirs(CACHE_DIR, exist_ok=True)\n", "\n", "def hash_key(raw_line: str, prompt: str) -> str:\n", "    return hashlib.sha256(f\"{raw_line}|||{prompt}\".encode(\"utf-8\")).hexdigest()\n", "\n", "def generate_dspy_audio(raw_line: str, prompt: str) -> dspy.Audio:\n", "    client = OpenAI(api_key=os.environ[\"OPENAI_API_KEY\"])\n", "    key = hash_key(raw_line, prompt)\n", "    wav_path = os.path.join(CACHE_DIR, f\"{key}.wav\")\n", "    if not os.path.exists(wav_path):\n", "        response = client.audio.speech.create(\n", "            model=\"gpt-4o-mini-tts\",\n", "            voice=\"coral\", #NOTE - this can be configured to any of the 11 offered OpenAI TTS voices - https://platform.openai.com/docs/guides/text-to-speech#voice-options. \n", "            input=raw_line,\n", "            instructions=prompt,\n", "            response_format=\"wav\"\n", "        )\n", "        with open(wav_path, \"wb\") as f:\n", "            f.write(response.content)\n", "    with open(wav_path, \"rb\") as f:\n", "        encoded = base64.b64encode(f.read()).decode(\"utf-8\")\n", "    return dspy.Audio(data=encoded, format=\"wav\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's define the DSPy program for generating TTS instructions. For this program, we can use standard text-based LLMs again since we're just generating instructions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class EmotionStylePromptSignature(dspy.Signature):\n", "    \"\"\"Generate an OpenAI TTS instruction that makes the TTS model speak the given line with the target emotion or style.\"\"\"\n", "    raw_line: str = dspy.InputField()\n", "    target_style: str = dspy.InputField()\n", "    openai_instruction: str = dspy.OutputField()\n", "\n", "class EmotionStylePrompter(dspy.Module):\n", "    def __init__(self):\n", "        self.prompter = dspy.ChainOfThought(EmotionStylePromptSignature)\n", "\n", "    def forward(self, raw_line, target_style):\n", "        out = self.prompter(raw_line=raw_line, target_style=target_style)\n", "        audio = generate_dspy_audio(raw_line, out.openai_instruction)\n", "        return dspy.Prediction(audio=audio)\n", "    \n", "dspy.settings.configure(lm=dspy.LM(model='gpt-4o-mini'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define Evaluation Metric\n", "\n", "Audio reference comparisons is generally a non-trivial task due to subjective variations of evaluating speech, especially with emotional expression. For the purposes of this tutorial, we use an embedding-based similarity metric for objective evaluation, leveraging Wav2Vec 2.0 to convert audio into embeddings and computing cosine similarity between the reference and generated audio. To evaluate audio quality more accurately, human feedback or perceptual metrics would be more suitable. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torchaudio\n", "import soundfile as sf\n", "import io\n", "\n", "bundle = torchaudio.pipelines.WAV2VEC2_BASE\n", "model = bundle.get_model().eval()\n", "\n", "def decode_dspy_audio(dspy_audio):\n", "    audio_bytes = base64.b64decode(dspy_audio.data)\n", "    array, _ = sf.read(io.BytesIO(audio_bytes), dtype=\"float32\")\n", "    return torch.tensor(array).unsqueeze(0)\n", "\n", "def extract_embedding(audio_tensor):\n", "    with torch.inference_mode():\n", "        return model(audio_tensor)[0].mean(dim=1)\n", "\n", "def cosine_similarity(a, b):\n", "    return torch.nn.functional.cosine_similarity(a, b).item()\n", "\n", "def audio_similarity_metric(example, pred, trace=None):\n", "    ref_audio = decode_dspy_audio(example.reference_audio)\n", "    gen_audio = decode_dspy_audio(pred.audio)\n", "\n", "    ref_embed = extract_embedding(ref_audio)\n", "    gen_embed = extract_embedding(gen_audio)\n", "\n", "    score = cosine_similarity(ref_embed, gen_embed)\n", "\n", "    if trace is not None:\n", "        return score > 0.8 \n", "    return score\n", "\n", "evaluate_program = dspy.Evaluate(devset=testset, metric=audio_similarity_metric, display_progress=True, num_threads = 10, display_table=True)\n", "\n", "evaluate_program(EmotionStylePrompter())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can look at an example to see what instructions the DSPy program generated and the corresponding score:"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\n", "\n", "\u001b[34m[2025-05-15T22:01:22.667596]\u001b[0m\n", "\n", "\u001b[31mSystem message:\u001b[0m\n", "\n", "Your input fields are:\n", "1. `raw_line` (str)\n", "2. `target_style` (str)\n", "Your output fields are:\n", "1. `reasoning` (str)\n", "2. `openai_instruction` (str)\n", "All interactions will be structured in the following way, with the appropriate values filled in.\n", "\n", "[[ ## raw_line ## ]]\n", "{raw_line}\n", "\n", "[[ ## target_style ## ]]\n", "{target_style}\n", "\n", "[[ ## reasoning ## ]]\n", "{reasoning}\n", "\n", "[[ ## openai_instruction ## ]]\n", "{openai_instruction}\n", "\n", "[[ ## completed ## ]]\n", "In adhering to this structure, your objective is: \n", "        Generate an OpenAI TTS instruction that makes the TTS model speak the given line with the target emotion or style.\n", "\n", "\n", "\u001b[31mUser message:\u001b[0m\n", "\n", "[[ ## raw_line ## ]]\n", "It's eleven o'clock\n", "\n", "[[ ## target_style ## ]]\n", "disgust\n", "\n", "Respond with the corresponding output fields, starting with the field `[[ ## reasoning ## ]]`, then `[[ ## openai_instruction ## ]]`, and then ending with the marker for `[[ ## completed ## ]]`.\n", "\n", "\n", "\u001b[31mResponse:\u001b[0m\n", "\n", "\u001b[32m[[ ## reasoning ## ]]\n", "To generate the OpenAI TTS instruction, we need to specify the target emotion or style, which in this case is 'disgust'. We will use the OpenAI TTS instruction format, which includes the text to be spoken and the desired emotion or style.\n", "\n", "[[ ## openai_instruction ## ]]\n", "\"Speak the following line with a tone of disgust: It's eleven o'clock\"\n", "\n", "[[ ## completed ## ]]\u001b[0m\n", "\n", "\n", "\n", "\n", "\n"]}], "source": ["program = EmotionStylePrompter()\n", "\n", "pred = program(raw_line=testset[1].raw_line, target_style=testset[1].target_style)\n", "\n", "print(audio_similarity_metric(testset[1], pred)) #0.5725605487823486\n", "\n", "dspy.inspect_history(n=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["TTS Instruction: \n", "```text\n", "Speak the following line with a tone of disgust: It's eleven o'clock\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "                <audio  controls=\"controls\" >\n", "                    <source src=\"data:audio/wav;base64,UklGRgTxAQBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAAZGF0YeDwAQAiAa8AXAApABwAPwB/ALwAjwApAKf/Lv/b/sX+6P74/jT/e/93/4T/bv8o/wv/G/8O/zv/rv/q/0kA1QAVAdwAbwD3/4H/yv9WAB8B3gE+As4BCwFmAK7/mv/0/28A6ABCARsBqQD6/0T/8f4I/4T/1/8jACwA4f93/wj/1f7R/hj/e/+x/+T/+v/H/67/of+R/7H/9/9ZAH8A5QAoAQgB2AC5AJ8ApQA7AZgB2AH4AbEBSwELAfwACwF4AeEBFALxAdUADwCx/3f/PwD8AJ8Ap/8e/y/+z/14/s7+//2m/Yb9avxD/H38Xfwk/Bz9Y/0a/M37V/v++qD8zP2o/qX+qf1j/TD99f07/34BHgKdAq0DxwJuAS4C4AKCBJkIigqCCc0GrASGA2kE7AdFCSUJOQjRBrgFjwSsBDcDFAI0Ar0ClALIAdwA0f5M/V/90/x0+3z5Zvnc+aL6Rv2A/GT7v/kW+S/5pfnR+6b8+P1u/+UAMgHU/+T/P/5v/bT/SwEzAzsFPQfLBSMEagKB/57+5QDDAwsGDAgQB8MDVgD1/bb8YPxx/1gBjgI3A3YAFv05+T73rfeJ+en8uP5WADP9qvvc+Pj1hPd8+QD95/9HAg7/1vzO+hD5C/tv/S8A7ACFAIIAKQCh/57/kgBX/w8B5wKUAi4C0ALbAcUABAN0Av4BRwIrAhECagPiBFwEmQPUAZsBsQFjA8UFLgZYBQYEgwPwAj8EhgRLBegESwViBQAEJgQwA0MDQwO5A0ECJQEbAZv+S/4S/n38Ifvu+mP4J/cB9yL2BvVN9Pb0QfPv8WzxN+9R757ygPSP9ZP01fFc8aHvBfLo9S73DPo//Zr7x/uW/e382P61AUkEQQaaB4IIhQnCCPQKDQsqC+kLyQz7DSEO2g6bDaALrgn/CPEJXwieCnoKJwYCBSADLwCRAeoCggHuAVwAgf8P/vz92fzh+0H/yv9h/wv/S/6+/iMANwNiAXgB2ABrAQgBfgJyCIIE7gVFBXgBiwWWCM8IIwi0BgwF5wI3AkkAGQCHAuEGcQYjAzMA7fx8+SP5mvfe9lv6lf4Z/c34//Hr6cvpMOwl8sn0j/Xr8m3raek359LpBO8X89H2PvcH+GP0YvIS8rH2pf6VBXcKEAgJBGMDWgMUChwQUBN2GHAXMBihFlAT5BI9EyQXRR1FHkId9xZjEGUNUAtdD7MPEBDlEI8IAwjFBL7+w/yJ+eD4Uvr0+wP5afV77sDzw/jO8sP0MevI4Wbx0fqdA+ACzfuf9ajykfY2/dH+/gGxCncLLg5qC4QGVQFP/jQCuQgDEIESZA66BkUB8f4x/7r7tfa7+sL9zgEMAI/1Iupw4HDj9Oao6azsKe0s6ZXl7uIw3KbcpuCI4W/ltOtb8nP0AvYu8yvy/vLx+pEBQAOLDXMPIRLbEcAPsQ7iEVobFB+NH7AgxSEPHYwghx+2HMUdYRq3F84V+xVVGbIUYRIyDcUEGgNUAuoC0AM3BjwEx/+q9yD04PM79m/5RfoA+Rf3rPUU99H2CPaA+Oj5Gf3F/c4Bpvzt/HwAUgH9BhQG5wKa/8n8/P0nA3QGwgQ7BQAAr/kW+eDzw/Tw87z1hfa68+P0Ye//6Hbo+eRc4T3kgOO85BbpyOkh5wborOTC5R/pKus98FXyW/srAiIFTwhpAKH7LABUBpQSRB8jIJkcKRgvFUwVvhZ5HJIdWB59I8MgrxyOHlwY5xJIFpsRnxC+ETMQGg+SDAsK7AQs/cL5dfq3+1sBJwdLAl/99PvA87T33PiF+jMAOQCJBJEBtP+P/fj5yvtiAXoDvASyCFMH2QSdA93/1Pt6/Ef/rQOzA1MDmf1f9c7yhvBj9NXxT/E3743rSOpX6y7rcOPn4iXhiOHw42vm5OZT5GnkCuMZ5cvhIeeN65nwfvpI+vH+hvnV9Sv/5QlHD2MYfxSSDe4NdhCWHBUafCAGJbQikCNrIl4akhHOFnQeoyTUJ08lgxdqD24OcA8pEDAQ9Qw7Cp4G2wl2B67/HABQ+Cb5kv1n/1QCRf5Q/C77L/pw/G/+Fv2y/s3/9QBlAToD6AGK/08E5QSqAgIF7AAB/37/V/8FAY7+Iv7I/oj66Pbw9MXtCuwF7kTrBe7w653n+uLs4Gzc1tuI3Qzd9tw73nLh5N683AjedNog3Jjmeenz7NDwke8m7Pnwd/fQ/GMIUxBcFGMPrg4FDfQF2RBdH+YkRyt1LrkknhqUGzkcmx42JVEnhig0I94i5B4DGIUV7RMkE+QSWRSuEZsNnglPBOMDpQDt/+IAwv31/V/9WvyS+iv6Kf1K/Kr8A/2Z+cL5tfn+/uwAzQK3Amv+F/w4+nn9/v4vAUQCIgFB/wT7dfpq90Hz8fYY9mb1qPLA78rrP+Uy5rfnJeYZ5Xjiltxq1+bY59oh29zcjds42efaVNoc2a7e290C4bvmkulJ6QztXvK/8SP9MwQYCT8IQA9XE0oPORSzGEAQiRwIKpks3DGwLOshnxn1HZQmhSotMJUywyjuIsciqRiYFa8ZxxqCHVkc+hdyDdgF4gQXBxgFNggEBuH+NP9B/w/+8/yx+x/5MPiX9937jfyC/fX+U/yY+of3Q/jJ+Z7+bQN0BswEZP+p+R732/aQ+BwAoQGZAG/+3/Xc7LnoFulx67fv+/Gt76TqDeTW4Bbc3Ng83b/dP90N3wzdKNq12cLY+dj71SHW+9lZ3ETjb+k56V/p7+no6QTvA/Wc/YYDfQdXC8EOvQ/DDwYZGhw7HuYkJyN9H6EiISPZLL4u4DC+LlIq3yUIJr4mtCZCKYUlTSixItseAhpcFfESLRMqEwUROw4jCLkHxwJWBAwEMwDF/tn8ffzd+5n9n/3O+pf7rfhe99P0MPiC+k8A2QOu/57+ufll9kn54fupAJADmwVKAxz+ifl98xfzQfNz+Cb5qve/9Z/tzuo26Zvqcukp6Z/lv+Hi4KTeL91R3rTebt6m3IXZftaL1uLZiN0D4HXhfOAC3dreSeEy5gbpEu5K8C/yPvu7/iECvgXTA/gFLgpHD3gRfhoUG5wg7iJfITUeKh+4IYIhtyddJ3crKSUOIusl3CCJIAEjOiM0G8UZlBrKF+sVshXrFY0PSw2nCnoH6wEmBDcC0AMJBEsChQDn+9b4m/rF+f/9rfyd/Bn9dPvC+Vf7Nv3Y/vIAqAEeAqr8Nv2x/yz+AgGvAMEFOQD6/3r7ePp1+vj6DPri+fb4Jvmo9of3Q/hh86Xxqu/n76Tux/Ng8AXuKu/36krneekm6I/pOen352TrEuo16vfn9el56RTniOq06sboDOmg7F7yWvBi8ZTzPPHB8o/1FPeo9mf/4gBB/yT/yAHJA/0CJQUzDM8M8A9oDbIMjAzsDIkQxhCRElkUphT4FbURgRKdExMQNBOhEpgV5hS5FDES/xSOEtATMBAIDjMQJhDZD9sReRCnDowM0Q0OClALxw7tCw0LiQyPDJ4K/wg1CdEKQgmmCGgJnAg9ByMMtArpCAwILwiYBXQGrQcAB6MHUQYPBdIEOQRsBBsCsgDcAEsCpQDu/pwAl//h/nX+1f0i/mD80/xd/C383fyq+9v62/rx+jf7wfpF+nn50vm8+Y343PiD+Cn5HPk9+K339PYL9nn1RfY19pH2MvaJ9bX1T/UL9oL1BfbC9bX2wfZO95H3Lfhq+C/54vll+qj6uPrL+tT7R/yJ/Wv/AwCvAE8AvwB4AZsBUQJNA5wErgXoBaoGpAamB5YH8AcSCbIJoQqaCncLwwuvDG8N2w2XDpcO1w73DpAPQBAPEVESJBJOEvURJBIIElESFxNzE8ATfRNKE/oSxxLKEpcSaxI7Eg4SfxHCEIMQIBCgDx0PpA4FDkUNeQzmC4MLNAu0CgQKEgkzCHMH9waqBj4GiAW/BPMDPQO6AoECJAK4ARsBYgC+/zT/wf5+/iX+zP1s/QP9nfxH/PD7p/tn+zv77vqu+mX6NfoI+tn5ufl5+Sb55vi5+HD4U/gq+Nf3fvce9872qPaO9lL2//W59Wb1SfU89Un1P/UP9eb01vTZ9Oz0FvUm9SP1I/Uj9S/1XPWZ9cj15fUf9jv2W/ae9uH2N/eH97r35/cg+Fb4ivjT+B/5f/nZ+RL6T/qF+tv6O/uk+/r7Wvya/NP8Nv2i/RL+Vf61/gT/Pv+K/+r/QgCyAPUAJQF4AbUBAQJLApECugLQAg0DQANwA5YDvQPjA/MDDAQZBCMEJgQsBCwEGQQJBAwE7APJA6kDpgN6A2ADQAMEA90CrQJ+AlcCKwL0Ab4BkQFLASIB7wC1AG8ANgDt/7r/fv9e/x7/5f6e/mL+Iv7P/az9cv08/Qn90/yW/E38J/zt+6f7fvtU+zT7/vrR+sX6m/p4+k/6MvoP+gz6+Pno+ej59fny+fL57Pnf+ez5//kf+ij6Ivol+iX6QvpI+lX6dfqi+qX6ovqr+qv6pfqu+qj6m/qe+pX6kvqi+qL6fvqF+nv6e/qI+nX6bvpo+nL6ePp7+n76e/pl+nv6hfqF+pL6mPqe+rX6zvr7+h77NPtR+2f7h/ut+9377fsg/DP8V/yN/Kb8w/zp/BD9Jv0z/Vn9gv2Z/an9v/3V/dz9Av4P/jL+Qv5L/lX+fv5+/pv+sv7R/uj++/4L/xT/Hv8o/yv/Pv9b/3T/hP+O/57/tP+6/9H/5//0//3/CQAJABYAKQA5AEYAWQBpAGwAbwB2AG8AggCCAI8AlQCVAKIAqQClAJwAmQCcAJUAjwCCAHkAfACJAIUAbAByAGkAVgBcAFYASQBPAE8AQgBCADYALAAmACkAKQApACkALAAjABYAHwAWABwAFgAWABkAIwAsACYALAApACkAJgAvADkAPABJAEwATwBWAFwAaQBsAHYAeQB5AIIAfACCAIUAkgCcAKkAogCfAJkAnwCiAKwAnwCpAKwArwC5ALwAyADMAM8A0gDMANUA2ADfANUA3wDyAPUA7wD1AOwA6AD1AO8A6ADiANwA6ADiAOUA2ADVAM8AxQDFALwArwC1AK8AnACSAIwAhQB2AHYAZgBZAE8APAAsAB8AHwAcABkACQAAAO3/6v/d/9r/zf/N/9T/x//B/7f/vv+3/8H/t/+h/6r/vv++/8f/x/+6/67/yv++/9H/1//K/83/3f/U/9T/3f/R/93/8f/0//T/9//9/wAACQAJAAwADwATABwAHAAmACYAKQAvADwAOQA/AEkASQBSAE8AXwBZAGYAdgB/AI8AmQCSAJwAogClALUArwC8AL8AtQDCALwAyADMANgA4gDVANwA6ADYAN8A5QDcAMgAzADFANUAyACpALIAvACyAK8AogCSAI8AmQCMAI8AjAB/AHYAeQBvAGIAXABcAFYAUgA/AD8ALAAmACYAHwAMAAAA/f/0/+T/1//X/9H/uv+q/6H/nv+O/47/iv9x/27/cf9n/2H/Tv9B/0T/Qf84/y7/K/8r/yj/Hv8h/xj/GP8b/xj/GP8e/yH/GP8Y/xH/GP8b/yH/KP8h/yT/If8e/yH/JP8Y/xv/If8h/yT/If8h/yT/Mf8o/y7/Mf8u/z7/NP8+/zv/O/9X/1H/Tv9U/1T/Xv9X/2T/Yf9u/3H/a/9u/2v/cf93/37/fv97/4T/hP97/3T/d/93/3f/a/9k/2v/Z/9h/2f/V/9X/1H/R/9H/zj/Qf87/0T/Pv8k/yj/Lv8o/yv/Hv8U/xH/G/8L/w7/Ef8I/wH/FP8L/xH/Dv8O/wj/Dv8B/wj//v71/gH/Af8L/wv/Dv8O/xj/FP8U/wv/CP8I//7++/4E/wv/C/8L/xT/Ef8O/w7/Af8L/wv/C/8b/yv/JP8b/yH/JP84/z7/O/84/zj/Qf9E/0T/Qf9B/zH/S/9L/1H/V/9U/1v/W/9e/2T/bv97/2f/a/9r/37/gf97/4T/kf+E/4H/kf+H/5T/jv+O/3f/dP+K/4H/fv+B/4f/h/97/4f/iv+H/5H/jv+U/5H/l/+U/57/pP+a/67/t//B/7r/xP/R/8r/wf/K/8H/vv/B/8f/1P/R/83/4f/k/9T/1P/X/+T/4f/n/+f/7f/X/+f/8f/h/+r/7f/t/+r/6v/t//r/5//q//T/6v/6/9f/7f/q/wMAAADh//T/AAAAAP3/DwATAAMAAwAAAAYAAADt//f//f8AAPr/+v/3/+3/DAD0/wkAEwAAAAMAAAD0//H/8f/0/+r/+v/3/+3//f8DAAMA9P/3/wAA9/8AAP3/AAAcAAwADAAcABMAHAAZABYAEwAJAAwAIwApAA8AIwA5ACwANgA5ACkASQA/ADMAPABWAE8APwBMADMAXwBsAGkAggCPAJwAbwBsAIIAeQB5AH8AdgBpAFkAfwB2AG8AeQBcAHkAaQB/AHIAUgBiAE8AWQBPAGkAeQBpAIkAkgCZAIkAjwCVAJkAbwCJAJUAeQBWAEYAbAByAHkAaQB8AJwAfwBiAJIAvACCAHYAggCZAK8AMwCPALIAeQAvAFkAogBiAKUA0gCsAMgADwD0/wAAQgCvAK8AGAGsAAgB1/8r/xwAWQCZALUACAEPAI7/Tv8ZAAkA4gAFAbkAjAD6/28AbwCMADYA4f/K/wwARgDiAOwA+AAzAGwA1P9/AOUAfwD8AJ8AvwBb/48AHACk/+r/AwDMANIA+AAMAA8BjwCvAN8APwDq/2v/p/8/ALT/5QDB/5wAvv9WANT/t/9pAG8AMwC1ACgB9QCH/xT/zf8+/48Ad/+RAfH/UgESAdf/NgC+/zECYf/xAbH/nv8fAVIAdgAIAbH/jADh//T/EgF1AZUBuQCeATwAjv9LAbf/TAA0/w8B5QC6/7IAsgB+AfH/tQDBAh/+OwEvAC4C3fxyABUF0foTA2H/CP93/24BZgA4AWQCKP+qAjMAXP2GA/b8vACF/voCGvzoBWb9qAG4AST/ngH6+3kEUvquBfv6YQKOAgL+HwAUAjH/Xv8hArf/FP/1/hMDpPtQB/z5ygYk++cCcQJB+0ADNP8bAj/+sQF5ADb94AJ++0oDMP13ArwArwAR/8EBYf/eAbb8aQQ//mcCGP6KAxUBjvdiDWX2QwTt/LkDQPwbAUIBN/xBAkn91gOe/oT/zgFZAE/+EwBYARj/OAGFAVkA6v/gA837VQE0AnL95/8BAnT/6AHT/CcHYPx5/VUFa/r8B4r3WgdF/oL+FwL8AMz90f8iAVADcftBAqUB/f81/g7/Hw1a72sOzPn+AZf/O/7B/0EGlfovAUUFrPlYARgB/QLx9uEKLfxT/LwIK/sUAi4C3P2g/DsJpvQ5BAP9jQdD9EMIVwL4+t0GLfQgFF3s7QaaBkL2NQkN+BgNLu4SDSH7EPyLBSD9i/7qApj+wAfF+XoCdv3KBrD4oQJvANwDP/mSBe7+tf76Ak34nwwC8pEO9PYGAOkHkvWuCvX1QAvO9kQCNQFq/MUJau9ZDFH//PmZAxcHb/HZB6IEYe9DC7H6JQUo9kAM0PMLCrj2u/7eDQ7rMBDJ+c8E1/s5/UMQLuu9B+UERvRmCOj52Qio7doSNO+cCMv+bvqlBD8EFvGRCiH/vPl4BVz93ADw+GMMK/MYBf0C2/JfEcnw9wYLArb4IAc/+YwEtfnbCZz1RQHNBy3sHQ/r8j0H6fwd/NMDSwVm8esJbfhABwv3SAEh/wgCUvow/fIMYe7vBMUERPd8ACoDyveTB/H6zgGB+ysCbAA9+LYD6/4g/akDFvlnAl4G2+pgGzzhgRaw9HQC+v/8+FADp/9l/u0Czf+S/XcCm/a8EObozBBP+Y0DhvTLCRD8HPlMBGAHFuErJjnt2PV5GK/hMBSf+YzxSBVH57QWx+e8ECL2LwD4CLfvOAr9+wT37hHD8LgBjwip8VEWGeXwE1v+seoPFYrzZwKfCGPwjg6X744GpAJr+pEBGf35Bz739/vyELbsQQo3A8D0fgXgAkr0jgk2/cUAcQIm/cYDjvv1CLvyWgti+eQB9PrvCCTzSwaPAHD41P+dAroCb/HuFT/l8hku6yIJUAMM+gAACAIM+lUBAAMo9sMPe+psGcPnyhL7+n735w7R+gn1OQi6CkP0wv34BbEC3vKmCIoKNujqEmr0LgYc+jQPtOo9D8fzxRE27BQHZhB619YofehHBwz+xQUbAe3raSDk4hQKxQiG7PoOH/rBApf7GA6V5T8lmdiiHSrv5PpyCaf7RwY16kUioeJ5CLMDH/rwAzH7TQcX+Bb99g/v8P8A3QZW+MH+iwWfCOrzPwGwB+D38v1CDTPsDRCP+ZT3WxJA6EEb6+2mDDHvZA6U/+HqShdy+SvusxjE9/XxRR0i5qsVUu7JByAIOfkjBMH3qg908zD4Oxp3400PzAAP8bMLEvqWBxjuLx19444WA/Th/4YLIOwZDKYDiPKPFMPn3gkLCiPwuxYZ4UItfs2FJiYAfdO/PQvSvhFg/BUJWPZmCNkDRO/+EYrvKxJ95+Yg2eB9Ey8Eie3SGLnwWgLdBlTzZQWJBI34MgXg/OgJSfU7EpLx7ARhBo7ySA1L+qT7exGd7+UJlwKG9bUIbP1PBYT3mPIaLBPHqCam+AT7qRDD7Esav920HnH3PezPHbDnRQ2vBLLt6h+R2mwVtwZt6/cK6ARBCi7e1x+D/HHuERqH6/YH+Qyr3t8lqO4jAP8AagM099n8ERfT7M8EivufEfDrYgWdAm/+3ANe/84BngUnAmbxmg9K9P4F1PffAMoGFP8dA0T3sA+O9wgFyfyWBwT3aAXD/MkEZvnk/3YMb+2+EsfrahNN8DoTd/8w6KIl6ehUBhvzwQ5hBjveeCY6+A7vQA9ZBDD0/ASo+okcCdVJHUX6Fvks/kAM1vyL+pYEkwvc7ZsNDPlf+tUVeOrYFeD0dvhLGrTqggEFAacTu96hFhH3cwuC9c7+uwkk/An9WwkT7MgV3/WUBij63Quo9tIAEQbA+HsBDA2b4lgmEd6HDloCBPgxDrPctzef2D/6QyB05/QCewkd+BQHVvlT/aQfANy+Ff7+kwNT8PUMrAhW5KUZaP4m9RwJ2/7BBWHz2hO364cSw/CTE+v20vF8HeHq8QG6CzzxSwZODrXyKQS+CXL14AMIAVb94QkP9b0LJ/OPFXDc3TMc1QQT7AC5/QT7xvx/EB3wBQWKB4cGEet4HZjhEhXq70sN1/esBKwI7/nyDJnxUAdDCEvy2QT9DuDw1wY7BhD5BwP8B7X6wAPYAKr/LwmZ8GsWG+5rFoPsyQcsCJ3zHgqe/4L6swds9aEFJvmBBmD8Gvj2CxwELP2VBfcC8QHV9coK4/yN9HUdkN8hGkr0EQbB+97+dgTz+DoLD+6oJlzQDCnI9tTzhxJD9R0PA/Sm+HUdq97RFhECLu8JFMz1IANN+BYYw9s1HXH7be+oEWb95PJMHRPknxii7ZYM7P0i+sYTMdolJi7nXv+NG9fiRgBJJTjSPRwbASb1rAD7AeX+OgPx+nYMo/g/AaAH7/AFEaD4gvonB+n82Qdy+fUMKfENF/borxW3AgvW4DR43sYQy+6kFhH/aOE2IBf8nNyLLkviIwNAE1jh6xnD+DT3WBGn93oCZfbQIw/VABzw+LTzdA6EAsH3Wf1AF+DjGQwEAsb0KA5e8osOSvMuEsfnrBSI/oTraB2D8M7u/Sfc1PoXrwTB8vQKu/J7EpDsrg2MCIDjEh6h9j73pQ32/FP0ohEb75ALE/mgA5QCcfcL/6EGNf498DAQo/ANGBTn9AF3C2nkFBOi9Z0CIglD/Xjyyg5oAb3vXQ9v/bvy8QnI+uj5BxfP6db8Dh7S1X0X8AsN2P0Tlf6M9UYI1flLCtH6RP/z/KYPkOToDdAHL+W/HKTuJgBf/ioT5dSgJHYQlMK8NUXeWQSCERfnYhn36xkIL/bNAwgFOPLoFWrfZBs88aT7tQVr/xb9bgI09/gEDPrXBncGouFwH0rvnvKWFHHvCRFR72f8Jx/c4Bbx9BpM/VLZ0j16y6QGthjk4q0X9vBZBDYAPvvy+ToHdALT7GQWYPT3668hq96KC68IIvpY+kYUfegmEBT/KOZUGz380OtsCL0Ce/bBCvHuoA+N9N37MPxJBOAL4NvfId/swQFHB8f7meyKB8QiEL9JLffqIPh6CxTvW/5nF5rnc/z2H0TfLwVcFUjuCO6kJxfbZQUpFdHaShuKA0DU4ye7ATnQ1yta7zXuXiJ034YQ1/uhBgT4Pvc5FLXhvQ8G+XP8QgGL8oMPUe+59MQikOeaAoYMmPZ6/E4CLg547l4KCPZwC1rzfvatA6oLJuVtF3b94OO4Kr7WoA9bCvf35f7d+/YTCOKuFo73g/TZGHzkfAi1+hH/O/ugC3X6te0vJYzlfAA0F6vhFggqD6P0hPO9GyXmC/8cHR/l7QLWA5wI4ukAC/sFlOY3G7DwavvVEZjulQU//XUJe/8/+Vj+lPN3IxbQR/+PNRvKig8nD5b0vOyVJQDsKfmjDyXyEQ6m5HUd4vG4AY7/wAddCxHbdBvs+In0nQ+R/0rzDAhk/7wEA/T/EFboxhxB7+T/k/zFDZ3zvP1rBYEG2eyiDAL6Qv7X/6/5DxFF6UMQtfon+JMDQwOK+3X+Bf6qC/H2Wf2pBNUMluDIHT309Pe2B0P07BhK4yIdZtxhJ87qV/vJGIzoDxGh9ob5sAtI8jwNMPR7Dqnx+wnV/onwHCFP1WgZbvI3An8M+OFpJGHeBRUU/DrsIh6b8tvqESdM6Qv/cQou/+378f+LDcfmoxTa/172k/hzGAvqOQQkE9vRfzro3fIU0vXsDDLqkAclCQ/uMwcfCbL9TfSQA68dCdmaBtcmzr3HN+LhWBWT3Cgqrvba3kM9Dr5CPsvR6BHnApb8+P0Y9vIhLNAcJX/1g/T4CK4Jj+nXFgj2PgIQB4rvcBxU26sR+Qcw9Pb87ww/AXns4wcGHGffHP0lHWnpgQbX+5kMLPV1/tEK/ACb8skLzQL07hALwQG18k0HbAwK4/oWj/1y9QkEgQZXAlLurBjU83b4OQzn+3MDuAXx9iwNifl99AQGpQ1H7yv3Dx1I3aoPWAnT4AAc7/my9cQKsQEXAj30qAls/Tr4hAbo9b8Z1uifBFYM++HVEbUAcvW+BXINsfaYBUQK4vFeAgMUsc4PMcnsr/nIDYb4JghS4U8pzeOyCI72EQ7h/yblIyDK9zXu1hMcAFHz9w5oBUbojxgo6psFDQsk75EOAPyWDCfz5wZh8/EJSiOvuKM4MgUczesyZ+efDOP4BwNSBeX5YAcS6bce3vbJ4E8lDvf59Dsax+aUHk3fVBamC2zlKg8+AjL+FuxEM7fOwx8U74ESUPRO7y8lm944FSLmkhlJBGL2lgSh8g0k2OEk+4EnDdtHB5YISwLI/nfzGBZ395oOJfLQA4kYTcMeP2r0adycJVEGbt7OFUUJIOzBItHacgiAB5gBtfJ2ELrzP/VmIF3jmQzzD9Tzq/6+FbbkERZvAJD0HP3LESf7efHTI0btC//J+SIhUeJJAP8gtN59DwD5pArWA1Hz/BDM9Y8ATCGhyh4vz/m28FcP+QOo/ureYkbKykALdBdz6AQPbgHD+KQGEQKm+HAL4u14HWL6BgDuCU0HjvaOEvrnMAyxCggG5OYrEmsWyt4GIYf3mQAr9loPF/MvDdv2zgU/Bbf7FwefAGkE2usmJPLY4CO3+6EGiO5XBygNEvZsCCf8mP40AqkImf3gAg8dNdFdMOnwZ/wKA6cG9gfS4XovkOhfAJcSH/mG/NcWofbd76sR1xoR0hYkJP/I7owQqfhXCkv2JQkA/cT3EBBx9wEKpQS59dYQwPg7BXQKYu13EvUABPtXBy0LHP4Z+d4N5/9PBXX6QAPVDBD0fQP+FtLtZQHeCez1C/8+Dsrz3vIsLafbYA+eCm34hQnG8JkQc/AyDW3w4QZ9A9gBufXCCF8RlOtnCxUNTutTGNDzpP/jB9oKN/so9o0TO/4w+FQOawEH7+UR3AhY4VQSYwzG6N0HfQc/BOT6kgjm+IIJwwMR848EdhQT6GUNcgmM9cEJuAEZ9c4NxwK08xINnPntAlv6PgZGCBfv6Q9OAtz4z/00DoT30wMY9pgNQAPe6jEOGQjK83sB3Qd3+z8E3/mICb30Zgz58JEB+Qv8+Gv+Lwn//T/9rgpe96wEGwGE/wcL/AT5+MkMWPquBZgFzfivBL4ND+6kDkwE4/wQA2YD4f6eBgQGwv3/ABsB7f9PAF8NMP1ZAJoCffzeAef7M/jT/LwAz/UH/L0Cj/Xh+uX+gPwC/lP9ifmO+t33D/pC9iD8Afcc+Wj6YvbJ/J72aP7f/cf3bvsvARH7nPkh/+n96PkY/vf/EP1r/3f/Bf6Q/B8Bxf4G/VP8kgDn+zj6uQM1/gz9rv6J/Tz9fAAD/YD8pf4P/jL+v/1w/NgA6vsC+t0CPADv+Zv+9gNK+PX9OAHy/W38Y/3FAWP9pf5r/r7/evs0ApUAQ/mKAjUBOvz6+80LVPvi/VcGuAGB+5QCDgLB+077uP6d/Lv6vv6D/CD85PtCAXj+lfr1/kL+KP6U+xD8CPq5+Lz5xflH/Ob8j/0G/Wz9sv1B/7X9+P1fAJv+Lvtn/Fv6Bfq5/Bz+lv1CAMT/R/+n/+3/d/87/ij/9f2T/GT72vv2/Nn9tP/BAXoDfQM4AZ8ESQToAbgBMgHU/zj/Mv7F/Zj+fv7q/yYALADI/s/9vfzj+BH7yfhO9nf3KPYB94P4R/hC+sr7YfsQ/D/9M/1n+8P8b/3v/XT/m/7+/qIAOAERAqUFqQRbBlsGhAYlBS8FsQXZA5kDSgNPBFIBDAAZAE8A1f4y/mT/5P+L/s7+/v7K/zj/9P/u/kv+8PzR+kT74fph+038Tfxv/VwAdwIjBFgFRQUcBYAHAQbGA4YE5gP+AbECcgQJBPADAAS4BXYH9wYOBsUEhgPHApkAjv++/q38vfvn+2z9pv3M/Xv+cf+/AO3/u/6S/gj+ef0c/kT/Zf6a/zMAoQHMBNUFagbXBnQG3QbdBtgFcgViBFMDowOgA2oDsQJwA38ETARIBckEFwL/ADYAtP8Y/xX+8v3G/JP8O/+X/y8A6gJhAiAD/wRTA4YDkQL8AKQCuAGbAdQCCAL4AdkDVQWxBU4G9AaZBDYEXwRFAdsBcgHq/7IAaQBiATcCUQJaAwkEsAM3AwQDfgKLARgBeAF4AR4CzQN8BCQG/AdWCJ8ImQjDB7EGkgXCBNkDLQPUAs0C1AJHAwAEzASlBf4FiAVpBL0CWwEjAIH/Hv/r/rj+NP9ZALsBlgM/BX4GUAedB30HxAYYBmUFBQXfBC8FggVuBc4FHgbHBrYHCQjgByQHuwVJBGoDkQLYATUB1QDfADsBaAHuAW4C0AJAA1MDMAP9AsEChALQAmoDqQMTBIwEDAVyBfgFIQaEBq0GgQZqBggGjgU/BfwE/AQFBZYEMwTAA00DBwPBAq0CTgLBAaUBmAFOAXUB3gHLAQ4CNwKEAocCdAJ+ArECCgNTA6YDigOdA7YD8wOWBCIF6ATfBMwEUwT5A/MDhgNDAw0DlAJXAjsCFAIrAg4CGwIuAiQC+wGeAa4BmAFiAYIBiAGLAWIBcgGRAeQBNwJqArcC1wLBAq0CpwKxAqoCegJaAh4C7gH4AeEB2AHbAagBqwGoAa4BsQF7AVsBPwEIAfUADwELARsBNQE1AT8BOwE1AUIBQgE4ARgB9QC5AJwArwDSAO8A/wAiAS8BKAEyASIBOwFFASIBBQHMAJkAkgC/AOUA/wAIAQsBMgFOAVUBMgE1AQIBmQBGAAwA6v/k//T/6v/q//H/EwAvAHYAkgCPAKIAUgAvAC8ASQB/AIUAqQDIAMUArwDIABIBSwFOAT8B/wCSAFwAVgA/AFIAMwDq/6f/hP9+/47/qv/R/67/of+B/5f/p/+n/7H/2v/H/93/AAAzAE8AggCfAK8AsgCcAMUAtQCVAJUAaQBGABwACQDh/77/pP9+/5H/h/9x/2v/RP8+/yT/OP9O/27/Z/97/47/t//B/9T/7f/9/w8AKQA2AD8ATABWAE8AaQBZADYA9P/B/6f/hP97/17/RP8b//v+7v4O/zT/W/9x/3H/NP84/z7/W/93/4H/fv97/47/lP/R/wwAJgApAEYALAApAAkA8f/H/57/a/84/xH/4f7b/tH+7v4U/0H/RP9e/17/W/9x/2H/Yf9R/0T/If8e/zv/V/+K/7r/5P/n//H/EwAMAAAA0f+H/z7/If/o/sj+tf6+/sX+2/7l/vj+CP8Y/x7/Pv9O/2v/W/87/yj/KP8k/y7/O/9k/3f/e/90/2v/hP+H/47/cf97/07/GP/+/uX+2/7F/sH+vv67/rj+yP7F/tj+9f4R/w7/BP8b/xv/JP9U/3T/bv93/2v/Uf9e/1T/Pv9H/0v/If8O/wT//v7u/uj++P7x/t7+xf6+/pv+mP6L/pL+aP6F/ov+nv7O/sX+2P7e/v7+Dv8k/z7/R/9H/zT/K/8U/wj/Ef/+/u7+1f7O/tj+0f7L/sX+wf64/rL+iP6V/mj+aP5y/ov+X/6I/pv+rv7e/tj+zv7Y/u7++P7u/uH+4f7V/sX+yP7Y/tv+uP6u/qL+kv6O/nv+eP5+/lv+SP5L/mL+O/5P/l/+Yv5+/n7+jv6y/sj+y/61/s7+y/7V/tj+uP6y/pL+kv6I/o7+m/6o/p7+nv6b/pL+e/5i/mX+Zf5V/lX+P/5V/j/+Zf5f/nj+gv5+/qj+0f7R/rv+q/6b/nv+eP5r/nv+b/5b/kj+WP5S/lX+Vf5S/lj+WP5S/kv+SP5I/kj+Rf5C/kj+S/5L/l/+eP6L/pj+jv6I/oX+lf6V/ov+Zf5r/k/+T/44/i/+O/5C/kj+OP4y/i/+L/47/jv+LP4//jv+KP4y/iX+LP4v/lL+aP5V/mv+e/57/nv+a/5b/jL+P/44/kX+KP4M/hL+H/4P/hX+Ev4P/gz+GP4S/h/+HP4P/gz+Ev4i/iL+Jf47/kL+SP5I/lX+WP51/mj+X/5Y/jv+LP4s/i/+HP4S/g/+CP4M/vX9CP4M/gz+DP7//Qz+H/4Y/h/+Jf4i/h/+GP4l/ij+L/5C/jv+P/5L/jj+O/5C/jL+H/4Y/hz+D/4C/vj9Bf7//QL+Av74/QX+Ff4V/hX+HP4c/h/+LP4v/kL+P/44/jL+S/5F/jL+P/5F/lL+O/5C/jv+OP4//jX+KP4i/iL+GP4S/hL+Ff4Y/gX+/P3//Qj+Av4P/h/+Mv41/iz+Rf5I/k/+Uv5Y/lX+WP5F/kj+O/4y/iz+Jf4i/iz+KP4Y/h/+O/5I/jX+Mv4y/jX+Nf4//kX+OP41/iL+O/41/kX+Vf5I/l/+hf57/nv+eP54/mj+X/5P/j/+Nf47/kL+LP41/jj+P/5L/lX+Vf5i/nX+b/51/nX+df5y/m/+a/5r/nv+df54/nj+iP6b/qv+rv7L/s7+xf6u/qj+ov6i/qL+i/5+/nL+a/5f/nL+df54/oL+lf6e/p7+pf6u/rX+q/6i/pv+jv6S/oL+e/57/ov+jv6S/qL+yP7R/tj+4f7h/uv+6P7h/tH+xf7B/qX+pf6V/p7+tf61/sX+1f7V/sv+wf7R/tH+u/64/rX+tf64/q7+qP67/uH+6/7o/gT/If8r/zT/Mf8+/zj/K/8Y//X+2P7V/s7+xf64/rX+qP6u/qv+uP7Y/uj+6P71/hT/C/8Y/xj/JP80/yH/Hv8L/yT/KP8R/xH/RP80/0H/RP8u/zv/R/87/yv/Mf8k/wj/BP/7/v7++P7l/uX+6/77/vv+/v7+/gj/+/4U/wv/JP87/0H/Pv9U/1v/Z/9n/2H/Tv9H/0H/JP8O//H+1f67/sX+u/7B/uj+Af8U/1H/iv+q/+r/AAAWACYALAAjAPr/xP+E/1v/OP/1/qX+pf5+/nX+lf6u/sv+8f4E/yT/RP9k/3v/mv+R/2v/Xv8+/zj/OP8u/xH/Hv8e/wT/JP9L/17/dP+U/7f/1P/X/+f/AAATAAkACQADANf/xP/E/6f/kf+X/57/vv/a/9f/x//x/wwA+v/9/9r/x//B/7H/iv9k/z7/NP8Y/wT/C/8U/x7/cf+n/9r/JgBfAHYAwgAbARsBOAE/AeUAqQBcAP3/4f/a/27/O/8B/5L+kv7L/sj+FP8+/yv/Uf+B/2f/iv/K/+T/x//d/83/pP/B/9H/+v8fAFkAaQBSAGkAQgDx/+T/2v+R/0H/Qf8U/6j+zv4E/9v+S//N/83/DAC1AKwA6ACVAW4BggHxAZsBYgFSAd8AxQCPAN3/a/8L/+X9n/0i/pn9nP2S/jX+uf3l/mH/If8zAMIAx//h/6UAtP+B/3wAiv/O/tT/R//R/nYAhQBiAO4BhQGhAYADtAIRAnYD+wEZAA8Bzf/J/Wv+Q/27+sT7Q/1W/dH+sgB1ASEC0AP8BGsFjgZqB2QG2QRWBL0CyAA/ARsBnv+0/1H/lv0M/i8AfwBiAMwAa/92/fz8Gf03/CT7svmx94X2xfax96n4svnd+/H+hQB6A/YHBAo9C04OZw/lDXsNrQsABx8FKQSx/6L6t/eK8+HuQu5x7pztfe/78czxufSC+m/9IgEIBr0HRQl6C+ALmA0qD6YMMwjgA6EBIANqAmv+Ufv091L2svlS/s4BfgW7BY8EOAWaBsUIzQcXAnX+sfo59Qn1APUy8ZD0X/o7+/8ApggJCE4KTxH5DxUR3BQ/DfADJgB5+RP1CPc+8nrrf+nr5vbo9fH49jr49/tG/dv+aQRxBpkEWAV8BGIBuAHn/6r8Mf/4AET/SQR9C/MLkg16D0MMfAwCEQkMvQMvAY/5aO6c6A3kyeAE44zgEdpX22/hSusn+EIAPwgBE7sW7xjHHg4f4R5MIRUa4A8lDYgFcfsz/OL9C/vB+1f7Xfji/ZYI4Q0YEsUVCRWrFToX2BWkFusVJA9rCekD8fr+9jL2vPCy7WLtbehR513sDu8k85n4IvqK+1v+b/0T/bEBegPhAdH/a/ry9bH3hPsC+lX6IPxT+Hr01/e9+0/+iAUJBDb9UQLrCR4KBgxMDDAHhwZGBFX6GfU79jv2+/HD7NDsEvKP+RUB5AK/BIALGRFDFD8ZXhqfFRcPNwaO/ur7k/jg8JzoIePx3oLdEuGN50Tv/PhgA5QKthMOHg0kpSnJMNsyCjB9KxUiHBkNFHINNwYUAmj+wPgn9CXy6fDR8uz1cfe390D40vVn8/3zXfT388z0Yvbd9/n8DAB3/1YEDAirCXAPLBB1DYcOEAQy8hTvOe1p6CLpwdqRwuPDP9BU2p3nyu+/7Xv2kwfMFIEijC25MDQvzStRJ08l4SLaGokMvP3A84ztd+dT4AXdFd544gLlkORW6OjxdvyOAgwF9AKeASsFWQTsACsBdgNOAUX+nvui+s0DbxFwFzwZ3RtDIEsm6C3uLssq9yZsIfwYrg6GBHL6Q/Qm7VbkSN0/3WPko+gQ7FHzXvv/AD4KUBAbEuEWdhifEXsJRQXUAi8Auf2D+BTzQPSX9w34O/qqAlwILwn4BBL+6PnB+334iO7Z5D7ap9Is0cnPJc1a0zfbQeNU7+38cQZGFLUhcCiHL7I1ejP6Kykl2BloEUMM5f5h7lnl5tvF1cbXPtbZ0yjag+Bp5BPwJ/di+toCYwe9B08NLRMNFFsWBhi+Fs4Z4x8WJSonSCoUK2Moaid4Jr8g2BlcEMcCsfZ17rHmzeMz4HXZStev3J3jBe7S+cX+8gRsEFQXMRuYIi0kcCC7HikZig8UCj8Fpv3U9jD0b/GR7y7vVvBk8376GAEHA+QBbAAY/zL+CPoP9T7uxuSk3mPY0tVg2Ivavdvg46rrve92/YcK/hGPGSMgehuIGQUaZBIMCYr/dPMD6L7iLdsg2IjZGts938fmhuxy9XADxAqcELEajR9zIMUhkyCqG6EaQxjGE90O0wv9C5oOoRLUFh8Z6RT2EwQXuxV3E8wQqAUd+IXy5+ux5njis9+R3kHj1Ooc9d37EQKtD1Id3CjAMLIxpS0DLCoouCLhGp0PUQIC9ufqzORC4cPfhN4Z3RPgKeUs7Zb4BgDXAvcGXQe6BrIJmgZ+/iD4tfHo6RTn0OOq36vh7+SY5VLpze+q83r4gf+pBPgFSQiuBf7+vvp59dTutOpx5pHeON4F3fre+ufQ72bxK/szBxUNjxXoHXwgciXRKiYoZSIyIakgTR96G7wU1A6qCucK0AtfDYINxA6XCuQF0AfFCJIAm/6r/ifzIPCY8pLpgeYD8AfwYPDf/QoH8Ac7DrAXiRxtJ9MwSivyJeIlfh4jGJYYcw/BAsr76PEc6TrrHusL5j/pgOxC6XTrO/Ku9tD8+QOpALX9nv5M/YP47PjQ+FHz4e646oXlCuNK5yzpUulK7CTvmO5U70LyHvMN9B/12/JW8C7u2ehL5g7nteUn59LpwOjD6Hbsje8/9ZL92QRECi4O1A6LEhUalxtYHtIldyPdH24iZSFMGZwhVCvLJkMcjBijD8sNFhh3F3EGm/rv8HTr0vER8//seekO587mwe4D+a7/kAPRCioTshVMGUEe/h2sIFAkUCCDGP0T7AyICZsJmgdlBY4C6PpM+Xn9a/+GA7gFEwBP+u76B/j49tn5yPXH71PsPOn35sTqf+1V7p3wk/B58GHzEfes+fr7w/gS9hj2WfG87N3r/uYx4wvii90I2gDcTNyL3uXheOKJ5ALpsOy58Krz/PTy9YL5u/pW/cj+8v1oAboGSAlIDZESSRR7GhQfwyD+JpcrCyr2LAQvVigoJmUqUSK7GqEaDg68BK7+0/Q88YLxTuoX51jmtOII6qn46/roAAIJhgwBEr8dUCDrIcgllSGhHmwdEBgqE7cSYwsACFMHewFY/t4BGAG9A4YHjASxAYYESwLQA/wDvv+4+mb5dPNh79zxTu6k7rjyBe606+zx+vM/9n/9d/vO9jf4gPSh8zf0APCb6ojlgd4s2N3br9jO1fLYPtZw0yrXp9vG3GzkZuhH527qoe5S7dTyFvkW9Rj2XvsF9oz5dgO6AkQGqg/IDWESER+2IO0j+C3DKD8ppzORLoUqBDDMJJgeNSKjGPIMyA3IAfz1cPiT8PPrxuzN5zbo++3S7Sn1GQDE/2YIZBP8EEYYOSHBHg0g4yS3H+MbIh3HFv0P2hKBDsEJVwvZBxcC7gVvCfQGZQnQC9wD/wTNB1UBlQGNA837UvWy9SztduyE88ztHut87Srrsep58XjycvK19jTzCPIj8fjtze/B7oTrsuXH3yfbg9yj29jZetsR14TWW9oV3Wje5uNH5wroZekm7PnwR+/U77fzcvJl8rz13/Xt8638rv4U/wwIKgtxDr4W7BzSHW0kjCkDKEwtfi/lLaYsOixKJ/shBiHWGKcTWguh/0D8T/n18cfvi+675bbosvGY8oL1HwDgA+EF7RNHF1gaHyEkI4kgSSR0I7seOhuMGKoSZhDyDLYLmgaNB8EF8AZWCHsJxguxCqALVwvxCZwI0QZcBIj+XvvF9mjyOu/X7/PsQum3693nJuiQ7Mvu5+/y8cjxA/Bo8hfzBfLo8Q3w6usj6OXlfuLU4tfe1typ3EHa/ddc2crartmo3ebfDd8K4MrjmOVN6I3sI+wJ7YPw+fDo9Yn5+Pos/icDPwWCCVwQUxSPGAQfpB/pIHkoZCsWLQAxly97Kn0szSt9I2Em+x2tD60LjQeN/FP5ZvlA7IPokOum6HztBPNL9mP4zf/bBVAPtxYwGIcfmCG0Ilkl6CUZIagevBx3F3QWyhJ4DUML3glOCpALoAsICn8M8gyaC60PBQ4oCtQKbAjCADH/uf1u9xz13PFv7V7qhuiT6IHrceq56JLtyO1y7cTykfPT8Afz9/MB73vuPO0d6D3nEuXY4eHeedyC2QTb+trr2XrbgNvd21je9t874pXlNujJ6EnpLOmj7G7yUvJm9NT3Qffx+g4CbASWB8oOyRBjFJYY4hmgH7cjjCWsKCkpGiSIJmQrYCSqI4sisBdzE+URbgqxBsUEhPut9173pfIr8zv2APTB9mb9yf0rAU8IIAuyEHkUYxjMHOIcChtfHRwdSBpfHcUZxxLOEf4RKRCWD30PSw1CDTgN+w0cEBQOpw7cEMgNdQk/CO4FugLYAJP8jfjU83Pwge+J7SrsOuyf7Z3rK+rI7Wvufu5/8W/xNe7U7qjuPO0C7i3sfeiZ6ITnx+I55ZPkpuA34y7ieOFo4n/hr+D14b7iu+I55eHibOGE5tDnu+br6V/tku0Q8Nn01vj++p7/KAWHBscK5w6IEV8VvBhHG4cfAiKuHjcjSSU4IjQnHicxHjkZQxwPGa8UWhOyCbEFqQNr/x8Btf1G+bn5ePoJ+Qr8tP/q/+QCywUsCKIM8Q6yEA0TNRV1FRkYHRhAFNUVDhaUFv4WwBPKEsoSvhEaE6sVDRQuEvcSixE4EZsSSRBWDPALmgd+ArEBKf1o+hT3B/Pz8CXuZ+vI6jrrw+iV6Uvqvecc6Y7q2+mY6sDrIeuU61Hr7+kn6+HqVel56efn3uaY5Q/ln+XH47/hnuIl4rbgMuGV4dvhpON25MbkGOaU5zjqS+5e78XxUvaa93v6VP9oATUFeQgBCosOkhEZFDAYfRtXG78c1SE7IhMgHCHVHbgZ7Rv7GToThw8UC9EGCwbQA8T/5f5++2L5xvym/Ff8rv+B/5wAmQReBewIeQwmDPUNQhEwEIsRFBPOEUMUqRRdE0QTQRKEEvUUbxUgFO4VTxVZFB4W5BaXFvUUHROWEO0OMAzyCFEGWgNiAO/51/d39y/ydO+K79zsjOkV6kXpnecD6JTniufz53no3Og56U/p7+mb6kvqQuqC6YzpSenn50bo9+c55arjGuQN5KTjMeJM4TfjIeKu4n/lVeXl5RroeOo27eTvAvJG9Pv2yfjG/Pr/GwLxBQ8J+gpIDsES7ROMGEcbNhygIJ8dQxxHHyof3BxXG0AXqBFTELgNegvICXsFxQEiAaf/zv7IAPj+G//MADwAqgKPBHgFTQdMCC8J5wqHCrEKzQucDO4NLg4yDcYMpw4qD4kQoxN+EjAUshVMFR0YlRl8GC8ZbBmhFjIVfRMvES0PZgwoCZwEnwDf/RH7offi9Ufzde4v7b3rKemo6R/pJ+eb5tTmx+bQ513ohOfK53boSehG6Inot+da54Tnq+Z85Szlr+QG5BDkzuLh4nDkJONT5HjmLOWH5zLqe+p17V3w5++x8m72xfUT+X/9YPzh/wIFEwRzBzwNjg0iEYIVhxa5GCUamBkKG0kdNxuYGSMYuhOuEmIRng6pDBEKpAZbBfMDtAJmAw4C0QEUAjsCDQNTBAEGxAZXB+AHeQiZCCIJqAl6CucKAQo1CZgJ8QogC6IMbg5rDdMPtREbEhUVARdwF0MYTBk1GXkYMBg3FxgW/xTuERgODAy+CYEGwAPfAGz94vnx9pD0B/Pg8LTuv+2E6wTrI+wh697qV+tF6pjqnesr6ljqtOoS6Q/ps+gy5hnl9uSq4/Ti0uFZ4AbhM+Cn34zh6+FV4prjJuXw56zp9OqM7WTv6fAz9PX11/cQ/IX+PADpA1oHJQkhDoURYBMgGCcX5xZHG78c1xtzHLQarhaRFvsVAxQwE0YQZgz3CpUJIAhsCMoGSAUYBQMEXwReBbEFAQaCBcEFigbYBYIFdwaKBvgFSwZ+BQUFyga6BiQH9Qm+CdEKHg6kDnYQoxTsFBsWERpfGRUZKhthGpcaURuDGAEW0hTuEcAP+A2KCpAHIwTx/8z9bvvp+HT3qfSv8dPwdvDK72Hvz+0M7UbtFu0s7brrmOpx6pjpluge52nlmuNR4ozhMOBg35ve3t2I3cXdod4t35zgZOJK4wnlR+fJ6DHrGO7E7yLylvR+9t/5PP0mANADoQbFCDwN2RBkE0QXGRhKF+sZkBymHIkcNRqjF/AXERdoFY8UPBGIDW8NAAzrCfoKEgkBBqEGdAZLBkMHGge3BlEGuwU+BqAHdwaLBQEGiQTsBOgFHwSPBJUFxQSHBkYIsgiUCmMM0Q0CEWcTUBRoFt0XcBgeGjobRxuXG0UaoxijGAEXthTGEzAQ6QtrCs0HVgRLAnf/R/uW+S34D/Z89ZHzD/E88cPw5O7n7yfv/+xP7Srs++rB6tnoG+Z85ZPkAuJP4RDg293r3bLdTNwm3N/cyN233sffz+AI4nzkXees6OvqLO3b7qvysfbc+FD85//MADIFvQriDJYPjhLCEf0S5hgIGkkZTxnEFtoWXBm5GAoXQhUIEngRRBIpECAP0Q7DC8EKpgt0Cm4Klwp5CJMHJghwB30HfgaJBOgE3AOTA3oDSwFCAXcCbgHRAakDBANvBAAIFgirCTwN2w2QDz0TthNSFcMXZxcvGSsaMhn7GQYZxBZqF58V0hFSEY4OVAreCTMH3QKLAdH/vfwd/A/6BPel9sz1xvSN9OTyHPG58Crwfe8F7v3rIurj6DHnLOUD5FjiQ+BQ3wvendt23K/cYNv228bc+tsC3iLhKeAb4nLlnOVA6LDsAu6D8Nz1yPbS+TH/XgLeBQwIXwlGDJ8QXhLTE5kUfRM/FZ4WrhYRFrIVSRSTEycTLhIkEiwRJhB0DsINAQ64DegN4gxACyALjQtHCsgJNQmGB5AHgQbcA40DlgOlAWsBRQE//m7/qAHSAHgBEAO9AnIEXAhYCUQKvAxhDkkQFxNwFPwUcRYtFzcXahcRF9EWwhVAE5sRphBEDhwMXgorBuwE8gTUAQAAxf4E/Ef7Y/zs+XP4jfhy9sL1P/b980nxU/Dh7lnsmOoJ6ZTmpOOU4uLgJd7l3erb2Nnq2lHaD9lu2vTaDtq23AXe7t2F4UTjnOQM6Rbs7OzM8ej1ufjb/uf/TwAbBmEK2QtqD5YQkA/HEoYURhRcFU8VYxS8FPkTXRMjFA0T2BFlEQYQXBDfEJYPCg+nDo4Omg6bDRkMUAt6CtUJQgmqBm4FyQRXAk4BcgFL/r/9KP8W/V/9Z/8C/kf/8AIEA/kDugatBx4K/g0UDzMQRBKjE5kUPxURFpEW3hVIFZ8UZxLFEVwRKw6DC6oK8ghqBxsGPQMfAcwAYgBX/6n9CvxU+/76vvov+TT3tfXn88Hy0PBI7vrrCOqt58jljePM4D3fRd4d3GTbsdpC2WjZRNqL2XTaQNzW22LdoN/G4P3j0OdZ6Fjquu/Y8qH29PrB+mP87QKhBjYIGgvxCrMLABByEbgRsRIkEq4S1hMdE8MTGRSUEuoSxBL/EKESNBMMETwRDBFDEAwRnxDODRgNrwzZCzcLxQgXB4IFuQOxAmUBEwCl/t/9dv1v/Z/9nv4Y/4H/BQHeARMDpQWQB9UIkQo5DGIN/Q7CEDgRUhHYEfERqBF7EckQdA7VDTUNeguECjUJ4Qb0BVUFPQPTA/ACTAAPAMT/6P4+/y/+T/rF+YX6x/dl9pb0gPBx72TvwOu26FfnqeQR43ji9t/O3Z/d3Nx92xncFtxU28PbMNym3ITe+eD74W3jieRE54bsl+/88PHycvVG+Wj+DwE3AhMEGAb4CFMMfw2EDocPtg8yEfESHRN3E10TwRKnE20ToBN/FFQTLhL3EmATfhLhEoIRJA/WD+YPbw2/DCALyQdnBy0HLASdAvEBcf+e/k7/+P1T/WL+pv3V/UT/Yf/lAHECDQNFBX4GygZ4CSALQAtlDV4OLw3hDukPKw5XDoEOkgxADEMMOwrsCGYIJwdbBmUFEATEAqcCzQJLATYAxP/h/tv+qf2B+276SfmE93L2E/S88VDwiO5a6zXqkOil5bbkLeNj4FPgluBF3v/cY9xg2xLdcd5I3bPb6N0i4afieOWR5qvmUeoq72nxCvRX95b4gftL/6sBAwTaBgwIdQmTC0sNYw8zELwQPxFvEW0TIBQNE3oTdhR8FCwVOBXAE3wU1hQmFOMTDROyERIRrBBxDjgNwwslCRwIBAcoBTADIQL4ANr/Xv97/t/9dv1p/X/9P/7n//T/bAABAp0DHwVuBuAHWQilCToL4wv9C1kMTwzDC3wMKQy6CtEJmAloCbUIcweCBSIFxQXcBAwEBwN0AoQC9AEbAZIA7v7f/Yz9gfuY+iz5yPZs9UfzdfHQ7/jtJ+sJ6TPooOfr5nrj1uAN4IXhPOEK4FjdrdvE3pHeBuB44azgaOEp5BLmk+gH7JLtIe/s8bD05/dh+/n8X/5eAbkDdAbJCPwI9wrlDGsO9g+QD1wQ+xGnEmQTNhQdFDkUJRUFFXIVWBZlFUwUyxVDFHoTqhMFEQAQ9g9YDWMLHQtvCH4GZwZJBAcDXgIJAOj+Dv8x/6v+5f5r/uH+FgBOAacCUQI6AxgFhwatB2MIXAj8CPoK4QpuCjgK3gleCmQKSAkjCH0H1wa3BoQGSAU5BGIEgwOzAzkEGgMBAqsBYgAGAAwAcf8p/Y770vmZ+Nn4Ufed88zwZ++r7uDvnO326M7mt+YK5xfnQuVC4aDf3OBT4Pzgz+FA4DffCeEI4inkIefr5v3nFeot7ODvK/MH9Pb0HfhI+sn9pQBSALECUgVXB4sJIAtXCxAMVw6+Dg8R5RHiEUQSThINFH8V0hV1FUUV1RULFqQWWxalFXkU7ROeEqgR3BBXDnINPAw+CtIIBAdlBaIErQN0AhECLwFmAGYAogB/AHsBKAF1AdkDEATWA0sFZAbXBjYI6QdgB2kIGAnMCHYIHAigB6oHQAc7BgQGwQVSBVIFnwRyBIsFzwRzAycDQwOzA20DJAKe/zT/5//r/rL++Ppg+NH3TvcO9wz1ePJG8Prv/+3S7Uju7+k96NTmPOUp5TnlvuKV4TzhRuDP4Uviu+FE45rjXeTU5sXpeOpm7FLu0O/M9KL14PeW+Qf8yP7kAZADowNDB5wIGAqWC8wMZQ20DokQaRC7EboT6hOvFDUV0hUxFqcXJxd0FmQX7ha+FggWjBSDE2QTeBFsEE4OeQyTC+QK3Aj6BusFNgQ5BLkDcgFiAewA2ABYAcsBkQEEAmECHgIpBIIEOAViBXIFngXBBrYHJwfNBh4GvQZXBoEGvgU/BUUFpQVOBggFdgT5A9IE2QSfBIAD+wFDA+ACPwFIAUIAG/9i/uP8uvsY+/r7avhU93j2PfQq9Hr0+PH07oHupuyc7CfrDeg+5u7mpeWg5BTj++GO4krjtOI14tDjQ+SO5jPoPejE65/tdO9P8U7zlfUd+JX6EfvF/ggBLgLWA+wE5waOCf0KOgsSDVEOtw7pEP4RLhKKE/MTyRSiFXsWBBeuFuEWrhYBFygWDhaZFDcTrRPqEgIRQA8xDhMMpguWC/wHvQYOBv8E2QSZA5cC0QEuAhEC6AE1AQUBqwGHAicDKgPNA5kDtQQiBRwF4QUuBi4GBQULBpoGtwYRBuEFrATuBcYH2wU/BOwDpQSlBEkEugLFAb4B+ABuAqf/DP5u/zL+h/sk/Pz8+PlZ+Qj6TvbI9tr3JvUr8+/xwPAH8CPwo+zi6bboNuml6RHnk+Qe42nkJuUJ5cnkMePc5Cjmree86QHrzOw+7sjukPDm9J72Pfgs+Zj6ov0YAYECMwMMBQgGbwinCnALcAu7DZcO7Q8LEukQ/xBQE+oTxhOsFCwVyRQMFTIVshRoFVgVwxN7EuIRuxGoESkQKw62C3ALhgv4CbMHBAYvBfUEhgQ9A6QClAL6AscCpwKXAtoCiQRZBNYD5QQnBs0GtwbhBm4GIAcpCPwH1wZqBx0H2QeNB2EGxAatBvEFSwV2BOwDCAVpBGQCggEoAXYAvADn/8X+X/5//ST8lPtU+7X69fkn95X1xfYX9yb1V/Mp8f3vXPGh7/zsOutY6h/p2ejH5xvmLuZM5cPk1uRc5WHmKucO5/fmFepW7DXuEe+a79jx4/TB9kP4I/mi+gP9BgB/AN4B8wO8BNEGjwilCZcKYwzfDDgOaRCmECURyxGnEtMTcBSIFSwVmhNMFAkV6xWJFK0TWxIoEmQS1hCgD1sOaw0ZDIALKApZCOYHswfBBQUFsgTFBHIE3AOgA80DeQTzA10DxgOZBL8EKAUrBdIEtQVBBtUFwQXuBS8FyAWxBYgFuwViBYYETARmBJkDmgL4AZEBsQHFASMAu/63/3T/df5T/fr7R/yN/Ir7Yvm5+Xb51PcB9wv3L/Uj9VHzsvG48uXxnfAY7vPs0+tY6hHrzed65yfn3+Vl5i7mJuWI5njmYea651HqC+u367TuxO7h8m30avT/9XT3Lvtw/Hb9Uv7N/y4CAAToBLUFxAa5CNUJawrDC3EO1A7UDpoPnBALEn0TgRJOEmATTBRIFUYUlxM4ErcSzhJiEaIQfQ+uDi4OkgyOCjQKuwlmCOkHhwYSBRUFjgXCBBAE0wMNA4ADJgQQBEkE5QRiBJkEcgUiBb4FGAaYBUgF3QahBh4GxAZYBU8FqgYnBjUFpQT5A40DDARwA7EBIgF8AAwAaQAx/2/+6f38/Sb9xvw9/N76NfrJ+an4bfjW+OT2BvVa8xP0x/OY8onxR+/f7Qbtie0M7Wrreehr5sfm9ujs6EXmnOW75VTnk+il6Z/pFerz61ntNO8y8ZjyN/SV9Y72WvjL+rD84PwP/n7/KwKiBGIEtQQkB4YIzgkEC28MMg1xDq0Phw9rEYESBBPxEqES+hLDEwIVAxTUElgSpREVEh8RRA+0DisOQg3TC8ALUQrcCI8IDAizBzMHMAc/BVcG1AZYBSQGhQWLBYgFTgb0BcsFjga6BvgF3gWnBpQGDQc+BqUFxwZ+BlEG4QaKBtgFUgUvBawEpQR5BB0DbgKOAQ8BBQEmAO7+KP4V/ib9EP1j/MH6BPtO+zP5s/gN+H734fa19Vb1c/Sh8m7y4/AD8Tbw7u7N6y3s0+z06vfqTeg15m3nf+mU5+vm4uXz5zLpJ+ve6pXpn+0u7zPwqfEX8xP1OviA+Dn54fu5/Tj/AACFAVoCEgV0BroGRgjcCHEKcwsjDH8NqA0qD+YP6Q/4EUsS2BE+EroSJBLnEuoSnhK4EQwRwhBAECYQEQ5LDUkMZgxTC4oKfgmWCGYIOgdGCIQGpQVSBTsGIQabBUgFigPsBIEGqwVcBGwE4gRnBu0GeAV/BEsGJAcrBnoGSwW1BXcGZAZPBVsFGAWAA08E1gM9A14BOwEPAdr/VP++/jL+Nf7i/er73fvR+8H79Prf+Wb4Kvh2+Bf3F/fs9AP03vLo8S/yUe8x7/vteeyw7HHrEurf6VDoAOj35oTniucO57HmFOe/6efqh+uQ67HqSu+Z8STzR/MK9N72L/ou++f7ef01/uIAvwB6A4YEyQT0BkcHIgnrCRALRwsWDHsN2g6AD1kQQBDfEC0TRBKyESgSOBLoEdoSmRBvEOIQPQ9XDoEO7wy6Cu0KoQrHCuUIcwexBioH7QatBpsFggQDBF4FpQU2BBYEkAMrBkUF6QM1BZIEMgXEBlgFPwSqBisGNAYkBlcGjwR1BRoHogTCBAwFeQSNAwkEqgLLAXoC1QAPAHIBW/+K/zj/8v01/pr8KvyO+077Efs/+pn4I/gr9072KPbH82HzAvL58B/xJO/f7CLtRuyQ657q6eis6GDoV+ce587mA+i75tfmEOgr6hTrBu0a64PssPB/8Y3z6fTf9SH3mfmU+yr8+P07/6r/uwFRAn8EdQWIBUAHrwg7CacKuguwC+8McQ7HDiYQXxDCEGsRCBIIEp8RWxKyEcYQ2RBJECAQoA8RD6kM8AtzCxQLPQtYCcYH2QctB6cGxAZUBlsF/wTlBG8EewUYBhkEogRiBXUFlAbUBu8EUgVQB14GcQYkBk8FYQaKBwQG8gSIBQwFqwVuBVkE0wNjA0ADfQPBAt4BfwCPALkAbwCR/7X+8v17/pL9Yfs6/CD8T/rF+WP4R/da9wX2mfQu80HzBfJH71rvZe6F7aPsIuol6hjqG+rT527mNebN5/noDuYH56Dnaekl6u/s5uwZ7XHvRvB08x/1UvYe92L5ePpj/Cj+kv58AJEB7gGZA3IFiAVXB6AHQwh6Cs0LvQvVDKsNvg66D/APHxFVEcIRhRGIEUsStREuEr0Pig+GEFcP2A3lDZwMAAtTC6sJpQnCCJoH1wbBBsUF9wbBBskDTAQcBaoGFQUrBW8EjgWaBmQGPgZuBQoHFAa9B3QGSwbRBrcGJAZnBlQGJwbxBfwEEgVsBKEFZgSDA3AD+gLkAuQCRAJfACsBWQD/ALf/CP+Y/kn9PP0T/dH7kvpo+or4d/fU9lL2lvRV8gXycPCN7zrvbO3t64TrOOql6dXpsOiw53HnROfu5q3nqejD6GDojuqu6lXudvCO7qPwnPGl9Wf3yvd2+BX6zfsc/gAACP8rATADBAO/BN4FwAcGCCgJ/gkkC9wMDg5LDjgORA92EK4R/xDlEY8RshEFEu4RshHcEMIQtA5UDsAPwg3pDPkL5QneCV4KRQnUBm0HoQZEBh4GOAWBBkkEWAWGBHIFEQabBXUFNgTnBpoG8QXaBtUF6AXBBs0G9AWBBqgFWwXYBcgFiAXZBGIFFgTsAwwEzQPZA9sBTgIwA2UBMQLIACwA7f/I/h7/Ev6S/b38oftu+jT7rPkd+Jj2mfVp9a3ztfJM8U7v9+977mHr6uv06hvq3+lN54fnrurc5G3nF+d75uPowOiC6SnpTuvE7h7vL/Ls8DnxW/Zm+Dr46Pn4+iz92/6U/0gBBAKTA9ADVQWbBcII0ggICT0LHAxGDDsO4Q5XDhAQzxCyEBURMRKVEXsSWBGIETIRqRADEK0PAQ5VDSwN9gvDCyEKjgmAB6MHAAhqBg4GlQX1BDQGBAagA54FYgX+BUEGggVBBgAHZwaEBlcHcQY9B9QGtwZrBfcGYQZCBaUFmwWfBPUESwVpBKUEMwSqAgADjQNTBDsCpQHRARsCdQGOAQAAkv5H/2L+4PzW/K37H/pP+s34B/gI9qP0kPQk8wPxnfBn717uUu0K7ELqv+lF6iromufx5j3oiOaE55TnSOZp6Xnp5emL6rTrke4G8dzw2PEg9MX1tPfE95j6YPxs/TD9Qf9CAcEC8wPdAnkEKwZnBwwJmAnbCZcKaA1YDWEONw+BDroPQBCWEAwRHxF5EEkQdhCAD7oP+g6eDRUNxgymCycLwQo/CfUIDAhKB7AHBwd6BsQG0QX7BZ0HtQUkBjAHcQaaBowIHQfEBgkI2gYcCIYIbQenBr0HqgbKBiQHUQZbBQgGMgXVBXUFTAQZBCMELAQgA2MDlALgAosBrgF7AUIAcgCk/1/+zP0w/UH7xPtu+jn5Hfju9kj2nfRR8/jxQ/Dd74HuQu2N7N7qEumR6mLpQedx547mVuht5+Tm7ubQ5wLqCOqV6YHrde2i7bDw0PCB8pz1FPex98v2q/pD/Ln80f+W/RH/fgLnAoAD7AOWBKQGMwiMCOIIOgv2C1MMxA4sDVwQkw+gD/0PnxBQEK8QuxGeDqYPZw7qDnQONgzpC60LbgpQCz4KWwmpCLEGogjjB4EGKgdQB/AGpgj6BkAHsghDByAIowdqBzgJLwkDCBYI8AYACE8IWgcQB1EGwQboBeEGTgYrBW4F9QSPBOgELwSZA1YEhgOOAi0DcQIlAa4BaQCX/2H/hP/S/Vf88Pz0+jj6lfqt9+H2+/ZA9ODzkfMp8dDv++7G7G3rk+xO6pzp7+lN5/3naehq56DnN+dX56DoD+lM6cvqZOtN7LzsZO/28B/yN/Rj9Fz1A/kC+qX6F/zT/Mn8ogBeAeUAxgMMBIkEywXdBygJkQphClEKogzHDvkPRw+uDm0Psw9CEewQXQ9tD/oOIA8HDwsOgwxrDcYLYQq9CgQLZApFCfIIyQefCE8JVghgBxYIsAdZCGIJIwgCCewIfwgvCZwICQjMCDIJEwj9BgkI0AcdB0AHmwXBBYoGPQelBcgFKwa/BDcGFQWGBJwEMwTGA4oDGgOnAtgBTgGSAFkAt/+s/aL9jP3H+yT7RPtp+Uz5zfcy9ij2MPQH9FvyOfEN8MTu3O2N7FPsKOoo6uboJujG6AroM+gG6MPns+gf6cXpm+o16mnsf+0I7qnw4O9y8lr0G/Z+9hT4X/or+wb98v3B/gkAHgKRAvMDDwX6BtcGxgccCVUJcwxJDLAL9Qw0Dr0PcxBhDoQOdhBjEIkQJw+BDgoPtg9LDhwN/AzNC2YMhguVCZoKBQlfCV8JYwfvCJ8IkwdnByAIhgcgCEMIhgf2B28IDAjNB/wHygbAB4AHRwenBu0G2gbNBiQHWwWKBrgFywWFBRgGyQQJBAgF1wLzA+YDtQGLAa4BEgFZAJ7/W/6I/gT/DP1L+7H7Yfsh+3b5fvc097H3Wvd69FrzePKl8izxHu9F7obsyuvn65HqEuq86KfnU+iW6N/oHehp6DPoYeoO67vqWe0T7UrvZ+9w8BP0lPNC9rT3pvgU++r7Cf1b/83/1QBCAcEC3ARIBasFpwZ/COwIEQrqCugJPw1/DcsNlw7EDnMPlhD6DpoPghE0D+APfw10DkcPdA6lDUoLZwtqC3ALeAljCOwHXwhGCG8IfQdhBnYHHgZHBtMHEAfhBe0GwQaqBykIKge+BQQHcAe9BjAHWwbBBuEGAQYnBqEGFQU7BcUF/wS5AxwEEATUAs0CRAIOArsBBgBWAJIAJP/L/qL9vP3t/CD8FPus+X/5b/kU+Nj2pvTc9MX1B/R98O3viu8R76zt2uvE6uTqKOps6WXpRuim6AroJugp6RLqe+qV6evqgOz/7Xjuiu+s8MjyOvT29I73LfhC+pf7o/x1/tT/0f8/AVMDFwIxBi8EMgV2CGoHXAhHCjgKGApDDBwM1QxkDjcPXg6NDxEP1w5/EJ0Pww/qDrsNeA11DV8N9gs0C8oK6AmLCQIJLAgQCGcHKgd9B2AHUAdzB3oGOgewB3oHlgedB5oHzAjSCHkIUwjjB3MHrAhLCUMIsggqB+0GOQjQB5oGdwaBBpIFhQU7BcMD6QNTBPQBhwIeAhIB+AAsAL7+L/6C/u38YPy9+276gvkp+Rv3xfY19n30bvPy8R/ycPCX74LtvOz66xrsN+uV6dPoEumg6Mzolugz6H/pr+lE6ybsces97J7u5O9F8t7yS/Kg9Bv3zfip+UH7rfw4/vr/bwAIApQCLwQDBIgFwQbjB6kIigc+Ci0LzQvdC88MIg2hDkcPFA/AD4cPUxDgD70Pvg7HDrMPRA7vDEkMugspDOYL+AncCMkItQhfCMAHpAbhBjMHlgdeBsQG5wb9Bu0GxwZdB2YIXwjpBwMIGQjBCXIIoAccCCgJHwkVCfkHgwfdBzcHqgfRBhQGEQbIBdIETAQPBT8EFwJBAtgAMgEoAYv+D/55/e38hPsl+gz57Pj99wH3VvWg9JHzy/KV8a3vQe817o/tuewx63vq/+mC6Q/pD+kq6JPoc+jG6MjpKOr06tPrWe3y7cDv3PEl8trzKfXu9ub4jPkI+3n9zv4JACMANQEqA7kDIgVyBXgFKQj8B7UIywn+CTMMGQxZDKYMdA4TED0Pww9jD9YPRhD5D6YPpg+LDsUNvg12DDwN6QvHCm4Kzwh7Cd8INgiKB0cGFweDB1cHNwdBBv0GwweGCKIIwAczCN0HZQmICesJiAm/CCIJHwk4CeUJpggzCCkILwiJCOMHmgfuBT0HagbVBT8FRwNiBAkEkQJ0AisB1//RAc/9hv05/Xr8sfus+Vb5jvbR9372DfSN88jxJ/Dc8IfvKe197D7ry+ob6mzpeejQ50PoDehR58zoyudw6K7qJeoO6wfsGe1O7qXxhvCu8uD06PUQ+VP54foq/OX+Mf+1AOUALwGgA44FhQVVBQQHgwejC+gJIgkGDDAMPw3lDSgOMQ6TDzcP3Q5gD9AP9w70DqsNxgw1Dd4NWQxKC/4JwQmxCh4KbwjRBi8IQAe9B8cGbgZzByAHTQeaB/8IQgkoCVgJvwiICbYL2gpUCpsJBAr9Ch0LpQnJCIgJ0QmJCCkIVwcAByAIRAZkBl4FfASrBb8EWgOdAj4C5AHxAfgAe/7l/dj+A/0a/EL6yvea99H3ovXk8zvyM/Ct787uNu0+6zHr8un26LDo8+dN5+3nqueO5qjmGueH5xzpTOkW6UXq5+py7TTvoe9Q8DjyU/SR9n34k/g4+r38H/7+/qUAaAELAosFsgThBYQGAAjhCVsJXgpUCowMOQxLDRgOqg4HD1EOdw5LDqoPQA+nDkgNCA22DBINBQ3BCs4JcgkiCVIJDwkABycHFAe9BiQHUAcwB5AH0AfGB9IIvglkCjEKpwoYCo0LbwzxCjcLUAutC5MLkwtHCsgJvQonC5sJLwi9B0YINQmTBz4GzgXVBTcGuQS5A5YDgANRAngBMwCy/jT/yf1R+2X69vh+9472avRS8l/xUPB+7t/s0+v46WnpYulD6E3n++Wc5ZHmDuZB5qjlguXU5irnpuh86TzpCOpm7IvuHfBT8PLxkPSO9p34nPnk+hz9yP4pAPgAYQIvBPkDFAa5B7AHWAmiCfUI3Qp/DKsN6A0SDYkMOA6jD00P4Q6uDdsNrg63DogN7AxtC2cLyQvuCf4JDAnTBwoHigcAB10HBAd7BSQG6gZWCJ8IkggMCCgJRwtzCxYMLAzdC9IMhQ0fDbUNkg11DSEOjg2JDIkMLAz9C/AL0QqaCuQK2wn4CBgJXAjTBxkISwaFBSQGpQRtA2oCzwBJACv/ov2U+wz5M/hH94/13vI98OvuXu4W7V3rmOlG6FrnCuer5mzlpuQz5M3jOeXv5PLkX+V45UjmDehf6cXpAevQ6wvvQvEF8lrz0vWO98z5VPtn/DT/x//k/1oCdQWqBmQG6gbXBhgJGQxMDAYMmguSDGEOow/kDroObg7qDnMPEQ9QDxEOmw3cDFkM6Qu6C7cK5QjpB+kHPAgZCC0HDwWhBZcG8AbNB40HmgfmB1gJJApqC9ALswtQC2wMKw5RDh4Oqw2lDZcOMA8eDnUNGA1bDVsNfAx/DK0LgwunCgQKDAnxCdUJyQfhBusFWwU1BSYEOAHcAIT/n/3d/Kv6WvjR9rb0K/IW8Zbw3O2N61XqMOiQ6GboqOUO48Hi1+Mw5Irjy+FV4bTiI+TX43zkPOVV5b3ntem76hbshe6w78Lx5/Mo9hn5mfmS+VH7gf8hAr0CgQJ3AuUEIAiFCc4JNQk7ClwMbw0UDocORA7KDjAPOg9GEPAPXQ9nDvsNIQ4bDsgNVwvxCeUJfglrCQwJBAZ1BZUFjgUNB+QGeAXyBLQGIAeWCDgKPwlFCRoLcwuvDPgN0gxiDRgOaw6jDzAPuw0rDocOQQ73Dg4OiQyfDHYMqguGDEkMZApuChIJggjZCKAHigY4BW0DQQK+AT8Ab/5b+/P4TfiV9kP0T/Hx7rLtFu3X6kDonOXv5Izk6uPo4VbgKeCn33bg099h3orfeeB84IjhruJh47TmcecT6NXp8u2J8cPwvPGH8zf3Efu5/O37Xfz9/2oCXwRFBYIFoQaSCb4JJwumDBUNmg44Dr4OKRBoEbIRuRBXD4wQ5REPEUcPPA1SDRgOPw20CiUJ0ggDCN0HVwcfBcsFDgbfBOEFZAZxBnMHWQjsB/wIugrmCzkM2Qw/De4NRA+2D5APwA8mEJAP8w8NEO0Pig+qDisOaw5kDkEOqA1sDNYLIAzAC1QK3glyCDAHdwayBKkD1ALF/tf7JPuy+Vr4PfRK8D7vPu597BXq8uVN5DPkWOL74RffBd213avd5tvK2xncqtto3fncAt1J4MDj/uHK49HmMumR717uL+2c8cL1J/hL+vL5T/p1/hIB+AH9AiwEZQUZCAUJXwmDDDwNog20DucOqBF9Ew4SDBE/EUQTgxS0EiAQ/Q6qDwYQgQ5TCz8J8ggICYYHlAZDBDMEsgTpA8kEiQQ/BGUFpAatB9kIyQgUCmAL3AxnDq4OgQ6tD98QFRHrEX8RshDFEYgRJRGlEYkQAxCgD9YPNhA2EPsNzwysDAUNlQ0aCxAIWwYKB2cGiQRpAAz9fvuL+mL5ifXl8R7uAOzH6wvq7+X34nzgod6E3pvd2tpS2XDX+NXw10va39ij1zbYm9kh3pLhiOGY4Vnk1+cA7AHv9O4X8Nfz8fYQ+Sf7nvvc/UkAMQIDBPsFowcfCWEKKQxxDgAQ/BD/EEgSgxOGFB8VaRRqE9oTIBSLEvsRkxBuDjEO0gxXCvgJwggeBh4GDwX0AnIEkAPUAaADegPwAnsFPgbnBqIIQwg0CuwMaw4UDzQPig/CEaATABRZFDoTWBLDE1wVlhTGE5QSshCYEZsSCxJmELgNNgxWDAgN0Av1CPgFpgPdAhcDAgHm/Gn54vUT9cP0PPGF7Xnp0uX25Bbk2eC43bvZt9ZI1mHWIdZj1LjRaNEU0/jVz9h82DbYp9p03sHiteWL5trnFOvR7jLyhvQ/9ZH2n/ln/KT/ywE3AoMDRwasCIYLIQ6CDTsOLBGREpkUOxajFJYUOBaPFU8VlxYjFL4RYRKdD9QOxhB5DFIJfgqWCLYH8Qk+BvgBEARWBLMDhwYJBC8BdQUzB9kHEAtUCvsJRQ19D1IRIRM9E/YT0hXUFocXJhjtF1MYoBeXFlAX9BZvFbkUZBMSERwRIBDrDbYMGwr9BgQH0QW3AsIApv2F+kr4V/fC9bjxWe2c6evmJebH40He9dle1g3UZtT50xPQwswMzSHOedDt0hvSZ9IM1RPY79wS4WLha+Ll5fvpC+5p8QXyyPII9vz5kv3t/xgBWgLyBD8IswtiDWcOCg8wEHcT4hUuFqQWshVvFbAX+hfnFlwVKxKfEXcSbxDhDnwMRQnrCVEKEglpCBEGYANiBMEFBQWWBBADewFeBZwI5gdMCOwI7gkLDkYQeg/IEbwUuxUXF8sZaBohG7sdzxwjHD4ebx3vHKwc2BmYGaMYdRX8FIQS9A6IDe4JsQZABy8FKQBP/XL6T/lP+ez0APBM7TLqIOir5gvi+tvx1nbU6NFjz/HNTcsoyaXJTsrMzBDPQc5ezh3TQNc82f/cPd/231bkCen+6qjuHfDQ8HL1tvhE+47/zwDeAYsFEglfDNAPFw9kDuQSchWoFTcXuxUlFlwYoBdwFzgWNxN6EzcTNREGEPEOSw7GDBMMZgyTC54Ksgk3B+QGZwfLBQwFzAQQBHsFHQeNB3IJpgswDFINzQ+PEQYUJRbEFp8YURqJHBogAyDPHVQfBiDHHpEeORyEGtEa5hdPFR0T7Q8SDUkIfgX8A3IBIwDk+8v2cvYz9aHzD/II6uPnnuqS5fngNd281D/Rys4oydzIIMeRwj/EtcUYxWbHrsmmy83ObtLo2W7er91T4JjlSemN7JHurPCD9Bf3L/rL/vgBHwQjCNEKdw5QE7IVnBjbGWwZwxyhHukcqRymHKIdqB14GioXrxUpFP4Ryg6WDIMLuwlTC1MLhgjlCDUJxQiMCFcH6gYaB2gF7wQhBlcGlAbeCaIMhw48ELYQUBM2GDEabx2LIt4ifiY9LLwtAi4tLCsrNCuqJwMlwiF7GhEW+BHLCUsGnwCH993zQvK07v/op+ev5J/hsOO94+XdKtvD15rS7tVNz2jFFMKjt7GtU7P+tZKwJ7KotGa7KsOzy4HSiN1a56DsGPutAgADiwl+DgURlBZVFTUVVhjMFO4VuxrnHlcf4x+0I1kk4yQSJeAjEyCPHfAXrhJdC6sBXfwp9dTvjOgm4ObcPduu1b7Wt9ru3ajmYesY8sn5gf8XB9AP/xRpGMobix6LIuwkSiv9Lw4zfThzPBU+pEOgSNpHgE3WUQdQqU0ZSupEwj6gNOkoyx6jD3YAJPfa6zXdvdPdy8jEzMD+vQ6+TMDAvt/AHMipyNfOddU81CXZEdYI0evVntLk0ijWS87dxnHGxckC1aHmcesk7+7+PwUkDkobACDQH9siqCFOFjgV4gzoBRwIwANkAhMIZwcNA6AP6heiGY0fQCALGp8UtQ2lBAH/7e/j49Lc9dTGz5TO3NBp1FzdUeYV7vT2d/+3AmAL3BRIFaMXuRgFFZcTPRh4HUcjaSkHL3I2GkBHSLhS4lpbWwtb6lSZTahGATynMwosjR+OFqURwAtSCWYIOAXU/9z5sfLr6S3js9uL0YnIbsF4vfO67r3LxUXJO9Kn3gHnGfHA+FEClAYrAnL+r/lj7Izh+eDl2UfPosiNxxnMMdbb4V/1igOvAKQK9A6bCbgRTwXJ+E/+8/QI6mrv2fBO7zwE+xGUG5QmciVCLu0rWSlPKQkZ/QufABrs4uEM4UHWM9jr3dLhrOhc7cL1cv4xAhECJAZx/z/6FPst+D38+wEeCmQTiRxGJOwxSUGJSclRW1M7T3xKf0Y8Rhw9XzEWLZglmx62IE0jGh+1HRwdiBlEEwUO+wUG/ezwa+Je2rbPA8jvxEbE9cTKxiLNQdLP3C/lLupa9xn53fcY/yz9sfoa/C/6D/bg7y/l995Y4UjZz9Qh2hDTMsn8z9Tek+Q6533zyf3U833wRwIc+VbwGwZTB1oDrg11BdkLDCHoJRgyGDeGJMwg1iAZFCsWcg1S9fb0muuM3LPg2t8x4tbkYOh+6jLl2+k361Lt9vQi8q3zJPeC/ZIMBhjIJbE3jj+ePj1NelQlSidUw1VjQVA9jTgXL90vYyx9KyMkcRYPGVsafhIoDQ8NlgS49tj1j/Fq55TnfOX63ojdn9nx1enYm9623L/dmOKU4uXpwO8C9ksC5AJyBXsJcgToANn9ovUF7sfiItE7ydq+47YLwcq6x7ouyu7FvdPg57TrMPyC/TjyVPtI/pfvaQQ1HTkUJiBsLbwosin8NERDmD7rMgAovwws/k/9ofIq8Orr/uHF2XTWftpA2wDgw+OE30/dUtkA2OPfZeoE8y4CUBB3EvshuzLwPCBNTFYLV4xNzUiYSmFDxkHoQlA9lTFKKOcmlCLYHYgZ+RQhCrL9afmi9UD0YfPP9S73+/Ex82Lxsu2e8lDw+OmH43jhKOL02szk8e7A64Hyz/n9/zQCbgL8CGwE+vse8+vqWdwKz9DPGsNiuNK8H7gsuOm/M8gs1PXZfd+388j2s/TUBggF8vUx/1MUKRBtJ/o/ty8fQm9GiC61Oo89hCJcGCgOSfGb4VDbDdRr3n3bGNK+2tzUGcyU2jbko9tL3rXlOeS46S/6AQbVEVojfi64OnlBpz+0R7ZRcU+KTDhL5T36M047yzocNbMwdCfgHw8VzAxhBlv7mfjg83PsFO9l7TzthvmGBMEFowNYBUb98vU8+dnwuOnf6fbkt+cI7qHvF/Ma/IUA0QEYAd762Pru9kbtPOns3EDPy81Ax2DDFMblwL69SseuycTK0tnR3vnche4j+V/1CwJyBAMIUBdAGCUmh0S6PDg/WlTcPSklIy2DJAj6xf2SAMvRX9RK22fGSNp73m3XDOVZ3FvVYd6D3BzhLOlU8/L9pwYuEo0fpythM8Y9KkglQ9c/WD9UOyQ73TuDPNs2RTKjMAkt7ChxI5YYLAyXAgn1N+/c8PboGe1p+Qz+dwKBDnYUTRNoGrAXTQ8DCBwEOvMG6an1Me9D7Kz5MvYw8Ab1F/Oc7V3wbOkT5N/k+dcJ1GrXgc650GHWDcujxzLFUsAoxQLNNtBM1CDcZOJK7/X5DwHtDv8UpBr3IhwtWy5mMBZFEkvZRSg6Eyy0Fl/5EftM9Yrfydxr0Y7Kr8xKz9bXc+R65x3o++nc2MLZoukL8q7+9xJbEsYY2yqfKb088UdIPr4/LDk0Kx4ryShVLvw42TVwNNkwnyXbIsciZxOuDZIAGe3r7Rzt3+SC9acGIAzCGY8d0RoQFAoTpRWUDhgFVPtZ8Svqhuwt+N38wf6uAVD8DvP76p7myt9F4XDfu9GUznfTAdNm1HXhxuBF1ebLT8nBxWW9f8Sxzj7OZNs65wLqGvTBAQ0TIByPJWAw1TbTPJ9BkElYSsY9rTNvGZ/5nPV+5h3g3+Wv0CzMidBXxh7K4uFT6Ejd+uui7Wzcc+jt9wj/WBVxIgsiICwPKaIxyz6zOF85cDDVKbglwiWZKeIpPy1uL8EvOyZ8HQkVJA6GA1QCtPtq6+Ps8u14+kYEQg1uGsgZYBQGGHkU7Qs+CigFlP8q9ELu3PCK+Cf8zgF9Bwr8+fTv8aDsA+QH28bYCNKGz73XZd1k2hDYLd9N2yDTqNEjy9rC/MSCzRrPzdfW34Djpuz89YQG7BR2HJgqojbWOOc/akw5SjJCYzR2HOn9zvLB5nzkZ+fszLrX08+pt4PPo9iR2ink7OiR5grfhe0o/jQLfiK2LPMsNCtANO04/DWOM+MsjyWzKBcfNiB2KNkczCVvMXkshSVIGvAPtgcPALn8vvpe9tjugPyYAfQG5g8iGWAb1xYzGEESkg2UAnECPwA89Rn5i/7f/cT/ivx89XLxPupp5UDkTNUpyOnQoM8fzcLd0drx1inZct2X27LUW9pt00nUutcz2HPYWtvu3oHqkPiu+pUJShPzF6Qn1TaDQIhKWFOmRZY4vyFZAG724eqA4KngcdqxyZvJ/cZuyZ3XA9z+3avhPeNB3g7nJ/hzBy4alyswNLs29DI5OdI+FjGOLpkpTSCUH2Ak0iXaI7ktETMaMDoolB5AEzgBTAT4ALn1S/ra9yP1cQIoCj4KOBbwEwYQIRP5EHECMANrCUP9Kf0xAuIA1P97DcYLfv4E+M3nLeDl2dDXSNYI0mDTI8uWz/3T4MvwyuzQS9LhyQvSENgeynbYxuRr1U3juuvJ6KTz4wPRCb4R3yWUKyw+p1DzVLdPhEiANPESCwY+9xjeXNzb5XDP4sg7ztTG0sww1H/YMdpP3TTaKOLI8oHzHgo/KRw1bj+kS99FvzVAOCY1pSVFHWYcKRinFvwgNCshKwUuuSyXJ5YcIAe+/kL2RvHS9aj+9f3o/v0Lsg2sEBcXVRWiDGsJuAX4/W7/3PSh9y8EsQKDC6UNNgzZA7n8Afe85Ovd5th/0EDPYdKq0hDUmttv3cvZPNXjyyvK984j0HjVudjT1yndyeS+3rLplPfB96IE4w/rFRcf0jlkRA1drWGyRlM0Ih59DxX2DfSj7M/Q3dM41s3GR8fp0OjNk8tr0mjZitO23MbsFPfNE7cn4TYdQcBEUkbRO5k5MjaJJRojQh1rFhocCSU8LUIyXjIQKDcbsxMuBmv2L/lF8g/x6PpdA6wEsQY/FaAPDxHiFMUJ6gIZBP0CvABmBE8Etwb+DXINrg3UCuv+bPGY6pTjK9bd0qPTW87lzeLYl9YM0P7Z09uvzMPHo8uAx27JC9q/3Y3fJuiv6ZfrpfZv7dP4fgbk+rcX7SvtRDlFrV3dXHs6mzY9HGYIZvWG6CLdB8/t0ybUhdWH21rTAtEp1NnL3NAV1UjafuZGAHgZvScAPZBIREgHTOZFGDpUL0cj3Rt0HsQj1SVxLxQ0jDVWMY4mwBuBBnf7N/gu7//sQ/Vq+0QCPgokDsYQ4wukCqMHWgdaA9/5JPtx+3ADSQxSEU0XUhF6C4IFivi28LTn8NtE1xDQGNJPyfzDZM4G0KHKcs0Mzfy/lcTp0OzQS9IE2wXep9/16WTzW/ab+nr0DPrKFgUa2C26UDJT0lIyU7JG4iVtIx4S8/CQ8ALd89OP1D7S682X1kTXL8zf0FzQc8s3z9TeM/TOAXUZdjCpNRhDXU3kR+08ejitLAcjYyBZHSYotyvwLy86iT2oKt0bZBPxBdX62/ba++j6QfvQAmQKWwbJB64RrQea++T/gfs89bf3wgA7AZsFKg+hDhUNUAevANr38PNe7mviDt6H1nvW0tUc0ffOVc12xPnD5Mbjv7bH9Mp904zc2tZP2VTbw+Bz7Mj2ke//3Fz1d//PCOk8K0+5VeRY/lcCR0czVytfECAHZvUI6hrnD9ne1vrb1N4J1YzYktTgxjTCvcqZ3O3nuf2EFj8l0zBmOXREREPcPV47ei/KI3IdxCNxJ6Yo8TedQE85zDBjKOgV8ghEBpT/jwDx+mD8mP4i/gQDVAZzC+n9dvxJ+S/ynvKC9Rf8EfslAbUJQwhiCTMMZgN2/BL6/e8c5QHfq9XV1ZTWiNGhzunL/8iQwxPIQ8hYxRPM3cs00tHeFNNN02Xm3+Uf6XfzDei45TX+0xsVMqdMjWEnWFFQAUhSMrQmAQ9lBeoC4u1K7L3jhdlm2H/ZTdcJ0DrPZ8JOxiTOOdTi7XgFuxlpJD0wGDd5MV857TtfNZk1dixQLFErrDHuOuA86UFlOrUyYCTwE1sKAAiWCAQC6AB5AGf8O/vV+vP8gfsU9xvz/vJk837yMfPP9aIAHgYpCD8J7gmWB0D8BPx19jrsPurh3ofbJtxA1DnQW9EvzTTGwcV9x63CBsSf0HnMGtOw28bTjdde3lHi2Ond7+/kzeuAByUV3jIYY5RcW1P/XppEYi7fJCUVuQMzAKX5uOrp5I7iptfu2sjdl9Lr0UzEj8TfzEDclvBUAnQerCWvLVYxGjCmNC8xOy+2MKcr2SgHMFM4p0OtRDxC7D1BM8shMxRZEHwIoQXdBu0CGvjl+Z77ifGt97H6WvDw77Dwluy96/TuQvZv/iwI/QobChMEnACI/ob13PH16bPk3+Db2RLdk9eezgzNccoYxb7BzsHRwQDLwswP0fjNUM+D1O7ZHO0p4KzsFuxZ4Hv+ARY8OipM2F9vZ35T2EpjQPksNRoXG0AMH/pt/LLtluTo4b/lJ99w29rXm8ESxRTKU8/V6aj6BgyWGMYgcCjRKmIugSuiLfonci1OM1wx2z6QSPRL8FANSC86lC+hHpIRMRLSDKIETgIz/Tf3k/hL9r3vXfCQ59DnbOSv3Yjq1+qu7oIBcwNBBjUJwwPs/UD83ft17hXqxOZv4QjiqeBP3UnYOs9CyV/EbsHRxYm8N8I3z8jJ5cTe1WPTEtWZ7ErrDt4x39zx1vD7Fa5CwkYZYopkxVZWVnpEszSUK2cXtBKmCI38BPeb6nDwl+fM5MTi7MxGzBHGeMV/0RrjMPT6AgQTORzjJP8kLylXJyooNi27KiszSzv7Qn5HuE6rU4dIvkegODokxSHRFooLNAYMBeX+CPun94zx+fDT5Fjh8uEM3a7ejOEC5TboF/T3+yf7PwCMALv+wPy19ZjyKusQ6GfnueCL4rfi+Nlp1CHSU8dVxTjBc79UyuHF68lyyAzQf9So2X7qj9nM5D7m6eA9B7seEzmhV+Va7VhSZ2BRjz2VRocvsB/JIIIVkwfQAzH/s/SV8srvS96mzyHG4L9Qx6nMw99O8p34FghtD7YYKhv2G3Ui4h1nI4kohSpfOUdE60+ZWvhaolImRWc4FCsKI0AgLBVICeYLwgTA+O37YfMu5s/h6dxe1svVTdNr0YTao9+Y5cDw6fCP8VT7ufj19RD4cPDp6LboQeen65/pV+ND4BXa5dFUzvTFncJty9TGEsVHxzvJEcq71W/YoM9G8dHW7c9SBSYArxgdSfZVUVh1Yxxi80iPRnxGbC3iLf8xSh+BGhcTfAimB6j6aPKs5YPXCMkbxZ/QbNTD5JDravS1ALIEsgm3DqURQg0jFHwZOSEPLlc4BUMxUMJaf1aiTjRDljjkL7gmlSVPHVUV6g7iCIMH7voV8pDnh9uI2STX9s9CzTjSNM+H0lTf4d7t4//pOOat56fn3+WB4t/lQ+gD6Kzpn+XW4LjdkNfR0gXWytLK06rPCczu0RPMENC71YbYJdl22ETjYN+v5YkEahOZLdpQqFPLU2VS3UhcQhFAezp4MnkxwDA2KdAn8R5/EMESqgIr9t/xhN611a3Xwtz64wfvfvaG/Hv/MAOlBC7/RQHoBLALXBQnH4UqNzcSQr9KqFPsSUhCIz1+MnQrIiaFJpUZ/Bj+GccKRQVr/ivyReXw47fam83lzI3HcMe8zETWk9hE2x7iP9kl1X/c6taU0v3f6OFQ3I7m+uYM2afeS96f0DXZbtoy1QvWpNou1kTT1t+w1zrbn+F44Q7j+eQg9JECeB42ObJKlFAbTw1JRj1LOxM5BDgYM9gyXDmdMPkofSgKIwga8hR1Dar7Rep/5f/g+t9C8Zv63/28AJ0D5P8R8w30QfN384r79QCHDvQaaST4Mjk9z0FVPi8+3TMGKdkopiB/HaQjeic8IVYc4RY7CisCnfTr6tzkKNXUzpHK3skFzcLRFdr01gHaNdkNz2TKH8jgx2rHxdBY2TfbnNwV2t/YL9li2fXZc9fn1kjeyeCf3Jjim+HQ223nw/B57GXuZvWF8tkQuTBHPGRLVU/QSJ43QD0qPAoojjJWNRErTTQbN7ks8yBDIN4ibhJ9D8IIGvDE6gfr7PFW+X7/yQeVAMf/vv8E87zx2exb6kj2GP5hBpESXhrRHsonRTI3MKko4yj9HzUZ6h6JIAYdnR+TJHMc5hS1EWoCyPZ59W/tgORd32bYQNSR1oPb2dwB20nYY9AuzrbLM8itxm/IWczKzirT0dJ/0bLQO9L30iDY3tlC2X3beN4H6+Lhxugk793jI+xa+KHy8PQwCxsWDCVeO29OBj3fObk96SgzMZIx1ydWKP4q8i3OLjszpC91JfEiViBAFxcP3gW9+AT3pvz4BIEKkQp1CUIFlf4F/q/1he1R65Tqru6z+L4BuwWGDM0TlRlNHNwc9hiYDT8NtQ3JC3cPPBAhDvUMMA+LDuQKvQbu/v/1OfHE6jXmwON74sTm2OkT7XjuFeqo5YvixN6S3Zfbd9f81OrSWNY/1fzYw9/x3oTiL+V15VTjHuO34g3fIeMp5aHmPuo36zLt+fB89W/9PQcjFKsaiSBkI90fyx0pHSwdABwaHyYgZR0qINAfLSBPIVgi9yIaI+shbBwMGQYUkw/SEAkR0xNTFH4SyRBgC0wIUwQe/xX+Hvsj+WX6nvr3+z8BuAG5A2sFzgWfBD4CigMPAF8AJAKbAYoCMQJv/mv+Of0M/eT/Jf6h++T7vfje9sH26PZr9uT2YPi093r3iPaA9E7y0fJk8/Lx/fNy8SfwTu9b7tjtq+6F7o3vZO998DbxDfAA8A/uZuz/7PzsLewl6u3nxOb+5grsgfLD9C77sPwP/q8AJQGkAhMDHAXmA0sGEAiOBm8IGQirCeYLJw9EE10TBRWjFKoSdxPaEywVbxV3Fz0YbhazFzgWqRQuErESshCtDy8RcA/xDf0Oew5KDwYQ2Q8kDp4NrQs4CckIvQaeBWsF/gXAA/QC6AFy/jP9V/zk+sX58Pgu9wTzmPL97wTv3PDn7wvzTvOC8avyovFW8WXyJ/NQ9PTz+/Jg9HrvifT/8Ybw4/S+7hfwCvBz7NTutuzo7f/sOu9l6rXtHOkm6E3oQuox6yjqKe2t6+7qTO3I7fPwS/Lc9P/1mfW69xn5kvrd/L7+WwEUAqkDDASVBXkICApmDE0P6RAJERES3hIzFNIVQxgGGJoXPxXBElwRyRCSEeATLBUyFcMXAhWlFTgV6BWbFgUVIhUkE+AP6QxXChEKDgopDEAMDA1VDZQKYwfNB80DBgS9AnkAK/+A/L/5J/hq9xL2i/Yf9jP08PNJ8aDwrPEQ8HnxpvA28HHvr+1r7oLtquud79nsyO0W7Ufrsu1E66rrie276ibsa+oy6S/pZebs6HTnwOgO6s/op+sU62jqbO0e7k/tae128MTvU/C+8u3zM/T689H36/Y790f7pPeq/BkA3gHYBUMHBwuSCSALjAz+DbgNfQ+1DZwQrg0ZEWwQjQ80ExIR9xKgE8ESrRNrEa0TkRJnE6QSZBJ3EhsSZxJjEBAQWw2qD1cLJA4LDrALOQxtB4oLpghDCFQK0AcPCZQG7gXUBqEFCAVjA7ECqAFsAOUAY/1//Zb4pfpC9gX6SPZO9zL2uPYo+uD3V/fU9530TPXM8IHvLPHI6QPwZeaH60rnGuid523nQ+hJ6IXpFOsX51npt+em6Hnppekm6QbpkOj26K/pA+xS7szssPA58Tnx6vNM8Tv3kfMn97X5Q/iP/X38XP24/lj+uQAVATIBQgW+AaAHcAeNB1ALuwmrDSAMfQ93D/0Ofg7ZEJYQGg+wEwoPBRGLDvUQZw/5EF0PaxHEDhsSJBK/ELsR9w50DnEOog2uDVcLqw1WCHINoAvnCngNNQlzC5EKmQhADLUJ4gh3C2oGRQVGCJIA3ASl/usB4f+1/W4Cxf01/lL6avyP+b76G/f4+TPws/j974D0E/Tg71jysPBj7GztWusu687qVOuC7bnsbevE65Dr/efr6oHqBOdl6Z3nCe1V6qbsWvBO68bwce+28F7yGvCy8QvzkvGR9x/1I/kf+av6H/np/Oj6OP6i/S7/KQDUAooDPwE2CA0DgghdB9EG+A0gCz8NQg3dD0wQvQ/ODgYQBA8jDCcPUxC6DiETIBCeEv8UNw/+FXsN0xO+Du0P1REoEV8NsBP1CVMQaw7pDFcPWQz4CLcOSQhBCgAMyAneCR4KqgdICYIEYgEECmQCMglmBDYEPQOnAs0Cgv6rAeH77/0g/Mj+/PjV/WX6+fha+LH2ePb78rX2s/BG9Azt/u468N/syu/m9MjpyPKB6wLqs/DM6KHuUPB17R/tzOyZ7Wfv9Ory8Qztbu7T8E7yXvNw9Db1j/Wm9Br4XfSK9/D4hvVo+lP5s/zZ/UL+lgNH/DkEgwNLAugF2wldA7UIQQZ2CLYHggiCDRUJCQySDEMLvBDfDF0TZw4MEQIRTw3sEHsNcwyODeMPyw0GEBIN3Q/tD+kMmRDdDgINoQ6pEBQK6hJDDPALgAveCSkMeQxXCjsO3QYQCPAHoQYPBQkIRwOPCCcGjgIGBCoDXQNCAb4BoQF7/xT/sv6O+wP9ivti+dv6tfq6+7H71/eW+UP5u/IE+NLx5fWZ7Q/yre8r9wbx4vUn8Hz1C+4g8LHzWO6B93fnTfiY6V/1ueh29BPt5vTE8hPsPfim7LT7bO29+Dv2FPhy8lj+avBW/UL2VPvJ+Qb5b/0R/8cCjACu/4wEQgDnBm0DlQVgBxMMeA1MBDQKiwltC34KIwyyCMoO3QZ/ELIJmw0VEp4NQxAFDQIN7AwHCykIlgsvDcYLcw+pDN0PDAxnC5kMlgyqCzkIaw37CbwIkgxyBBwJlgelAEcK+ACqB6ADsgkyBbQC8gD7BUYAJwJjA0wACAHV/fX9hf4zAJb95ALt85v6gfcg/IX6BPgD+bn4FfrI9lb4mfho9pL1I/hU83X6/fNr8i30X/F+8pXyaPZG8Eb0iPKZ9Bjy2fBd9FrwBvHu9kD0tfUf9Zz5cvXf9fH2jfOh93n1Xfiv+Tr4ovlb/3P4ov15/Vv/rv9kAu4BLQPqBr0CDwVuBfADpwKoBfwE2AXSBFsFdgx+CXcK5ArcCEQKZw7lCMoTmwnRCmwM9QxbDdUNOQySDW8MPAyuDoYMVQkRCtkLJwdjEGsK4QlmDCEKMwz3BpoL/gH6BgoDEAQUC8QGPgYvBAgKWQD7AXoDof96Auf/agPVAGv+1f5y/rADAPXUAob5Vfp2A3f3QQI59Rb9Y/j89Uf4qfGO/z30rfeS+mj24vXO9gvytvjB6pLxS/Iy8lXyR/OD+BL2KvS99Bf3FvXg9NP0ffhh+1bxaPpz9Eb0sfd3/7/1YvoU/Mb00ANQ/Hz90gCHAjH/jwT0Bp4BpwruAVwIRP8VBcsFHgLkBs0KHQt+CukIfwxqD4MH8gxsDPkLFA4WEHYMpgvrDRAIgwx3DjYI7QtUDhsODAlOEogNQQqKC9wM9w7bDfwMjgrWD+cKcwxUCocK3g1rBQgGRgiNA7UBAAt8AHUFpAIDBMUB6wUXAzj/gv6j+JL9qfHjA3zxu/6V9lL16gIe7qj+ePbx8lb1yuuq+w/t3Pmk7/n4lvCf9QD5a+or+iLyKvAW9eDnD/al8Zb89PP/7UsNtOpEBjzxkPQM+WX6eP6z9Oz5Z/zQ/A38tfrZB3T/NQFI/nIJnQJb//MLDP5DB/AGwAe+DkkIiRDvAAUJTgZ+AngNvAhVDSAHFAvICYQSwQqMDMoO2wmoDZYIvgnKDiQOBQ4MDYsONA/jB44NSRBPDB4Sww9tD7kIHw1rCkAP4QWNB84Fjg1qB6APgAcnD34FvQrEAqEFEwgzA3AHOQi5+eX9Z/sr/67/xvy89Y72RfI39GH3n/Xv+dj1ZfbG9Af4qvMr86Hzyu/p+C7vZ/M16kntuu+48Xn5JfIj9C7vie1j8ADs0PTI8eLxNvWl9gfzovpt8Af4WPLV+ob0Ff68+Q73fvcc/lv68vX4Bfjyd/9WAAv7FAeL/mUB/ftqA7sB5QTBBQECW/5JBFsFAgEXC6YLO/7pCDMHmRBL/zcPpQ2TAwoTXBAZDJoKdwvhCYES8hTZC5gJ9gepEO8Ihw89D/QOIhVQGEQPzQ/8FOIRpRGGDGkQ3g2vEN0LNQ09C9kQJQ12B4kUVgz3AocKhgfJA/P8yAn5A6UBzfsY+08AevwvAPr7q/7w9Iz5HPVl6Yn5pvAU86T7sPyo/gfvCPri6anxGuvx6lrzpOsF6vfz0+wr8t/slOs395bw3Ox+92Xp5/Mu69fzFvHL8l7uVu2o7gfvCfX49Svz6PEp9Z30+vcy8cX56PIi8m76ufUz/VP8ivvH/+wDAwg8BF4KMQqODYgB/gX3BiYIEAtkDqAPZxNjEO0KKQyQC90TjQ+qF/MT2xmiFeYPaBEdHDQPbxnrGfIQwxcJEO0SfxS7GY8VMBxlGQ4aUhmWD44WqQgsECQPMxiqDwoTewkIBvUVeQhvDNQGI/2r/l8AH/4/AN4JA/0AB7sBHACK/PDz0+vO4r3re+aS7Urrx+Yw7F7qCfF/7ZPol+NA7JHi3uqD6J/l2t8t6I/pLusX9Hjq4u0P4crnxeUa7H3n7u437+rvOvCx9tjxsfrS+eH6u/5QA5QCogl3/3gBFAcIBVcOEQ+aC2cLOw5yDSoLXBVDFIkU7hW0EsQSGRWIFZ8VARerGtIY4hiHFucOYA+WDzALjxUmDGwQ3hLSFOcWiBoYEncSexLJFH4S4hHKG3oTVBKbGVQTfRNzDDAQ8Q4cDdoOKxJjDEoL/AcYAd8EffjF9sTylO9Z7Y3rnPGg6HHqR+ur5ljhQuHp4M7VONK+2kffbNmy3ajdtN6s5Z7iYN8e3tbbqdiB1njdjOGX3tbgu93d4ybpKu+68473mvNm9Cj6X/kF/hUBtAYmEOYU4hlPFVoT6RBBEiIVlxZMHRobkxjUGv4ZMhV1GcoWihefHAwd2BlGFNkITwVjBy8NlxIrFgEbFRGYERsSLgo7CgwIvwgMCS4OlRHEDjgOXwnkBYIJbxDuCcUNnglyCXkIOAmqAooDUwdH/ykELwmY+uPwAOzI4avaV98l3rraedQH1wrXd9rx1t7RQs2CyarOd8oL0hbUP9EV0QXa4Neu1ijW+c8uzsHOCNZa23He5eUz7Fr05PpPAFECUgDhAdMH1Q3iFFQbpB93I/wk3iacJckggCTSIQAkPyU3J6EmPCEyJX8lqyY/JYAgUBClCb8EpQFRBosOThIGFL4V2hbVDQoL5ALR+1UBnADNBzQKuAEDBKoG3QK1BeMHMAdXBxoDkAM4/kf/FQG9/B0DTgGh9//5pO9X5/LkQOCZ3DDYkdLZ1BXNeciyyPrC5MVmxwfLfsrFyUrLT8y51NXZd9b+1fnTtchfyBXN19LG18vqJ/QjADwEkgVqAqwEGQjCEIgZsSI9LEYtTznVOZ47fjulNuAwfi8xK/onlihvKUQzOzOHN700cSd3GxgOqQBK/G/9KwVVDRsWWxpQFC0L2gLq+8r3MP09+Jr8AwAuAq4F1QgKC8kLPwnxCbsJFwKR/5z5Yvnv+W/97APPAHX6yvN58LTn/d9B2mPXO9YM1TrPYc74vHW8LsL3wsLIhclCxJq+H8gc0bvV+OEa0wDQp8qGzNzM79QM4RDsvv8WFPIQLROZELH/vhF6Ixcwnj+/RdxFXkccSjJO+UkyQg5D8T/QPNY4KTXeMg0wJTZvPYg2FiRJGFUBbvqL+oT/9AWhBuQOMQ5DBxMD1Pc47iHzTvPh+2IBxQBR/+H+pQnxDekMwgyrBa0C2fzt/LL+gPicAEQCS/7l/Yf3JujB4lzclNLl2YvWmM3JzxPIq8HQv8vBq8GoyfDH0MMDyFzUp85Y3jHeJNLF0OnTW9Uk2wHzF/c2AIMTMhk2GMobDxkaIHYsd0h2UQtXG1deT45LwEwDUZlNCEdBQ9JCsj1KQLg2XjKIMlItJimLHiAQiP7u8mr4e/rhAc0HSAWP/Vv6JPs68PrnoPAp8VzxpwYTBMgBVggVAUEC+QcoDk4GmgKvBKv+WvxyBOL9aPoc/mrwfO1Z7Jngk99Y0W7W488pxHjNccEtuynA570vxFHGk8OhzhTCAc6i3Z/M+taC2YLQrdds4Cbx5vyaB9wcbxVvFZkYVBNZJLY8KVb1YoBxHGO9VBdUkETCPtpIk0nIRspHnUwdSTYxGT3GMIkgpxtgE4v+tO7S8cX1qfgb//MLkf9q8zntqOEb2pvdNOMp7fjywf5ZCMr/LwDwAzD4sfuj/Cz+/f9y/jMH+wmZCAIJnwAy8ovqeOaM4X/dOeTN45fexNpv1U7KD8GfwOu4ebumw6/AT8R+zkvSLNiR2lDTiNHC1CbYZNrf6efz4v3aEmEalBrdE9cSoR6sKYhO1HC1a7Fk5GDVUoo8FkGYQn1AUEXIShRUuEI6PEE3uypkH68c/Q7I9nTvf+2i7eX9HAgEBiwAa/bo5eHRSNZt06/c1O8A+KwET/qI/mQCI/Qf+br7wfYT/W/+VwJiBX4J3QtIBfQCQPgi5krbkt0T3AnlTO1z5CngkNONxojBMLdRtlq6lbxSyfDKY8tG0KbMjtF+1hbUntVU28Tabee69+T/cA/1GRIeUBx4HbcrKifbMu5rSGziZv9/uV6WQeo78kGaQPBAdl3WUfk4JEiuQ8IlSR0LFvgFTfDJ8Oz5iehL8roGovqBArn4pN4x0iTGJNIr3vTmIv4T/UgBfgkf/jT/O/py8nb03P3wBpsFng1ZHE0TTBHwCzf4QOzm4zveFuTw65Xt/+yQ67TeX9BmxH+8pbS9tg7CY8NFyRbQQ89D0L7SlNaLzRfP+dQQ2I3jPPFlBQgFBA8PHVQW7hkqJyYtHzIJSg1yN22qZE9zpEcYMwE/ijyPTtRT9VIzTVA9FTqNL20gJRFTA9H7iflv8c3zz/U/9qIADPrD9KDjPcuRypXRDNXg6wb5MvaXAo4BGP4v9lPwdfKC9lv7pQS8CAEK5hSXExcTSwbB9hrwq+GA6PTqSOqx8xHvs+jo5UfTXsbTv3W8s7+bwV/Ijcvl0PjRINij08jR29EvyTvWHN1O4nHv8v2bCS0Peg9qF2Qa9RQgLC8+iT31VmxqLGvPZgBZZ1CjJIkxBU97P8he7k+4Piw95SUOIncbvwBM9fT2b/Xt97n1yPZm9WDwsfdt50fTc8vvyOzUaN4a+OT6gvW4+tr7xvQh77jyi/LS9SECtgeICRMIcQ5aC+QGvgGl8Trn0eL65uTugevr7f7uNd7P2DTWZ8Y8xMXE08M6x4fKz9AQ0BPU4dbe1cPXNNKv1IbcCt9n6xjyEP38CBoLkhEREnQW7Bx2LbI6QEkYW15k72InZAJScTvYJQ8+tEcCRs5nr00WORAwDh9BH0sKLP7x/x7qhvgdA5n0WvcH9CfwGuj23/zYJcHZ0I3fMd7uAVX6Qe/p+JDrLu8t7PPsN/jP9UIF/QtADCcGNwOUBr/9I/VM8dXl3+S06+vuA/Bl7iLlQdoF1QDMxcyKxhHGksztznLNp9I61/PP89M62ynU39wx3jbkD+6i7Wr4KP/TB/wIBhA1HssR/yi9NAs7pllBT8pxGWZzTQtfUjGuHvNEykSFTm5ozkazROwcVBduGtv6HAjn9zf0PAD2+G769/Od8OTu0OOk5ubPKsd71QHaV+vIAQ38bvZq9GXpgOzS6dXtzPn8+EgJ2QvpCJ8E/ASv/VL6qPoc6azl8OeF6cHuB/Mq7Fnlftrp0BvOrMhaxo7Nks3G1GvRBNNx1uXQWNn23FvaI+At413sTfBK9DP9lv3pAyQObg5VFVseRSHbMo9BT07bWz9atGQAYUlBEkYFKiQv2E6TTepcZVIcPeofhBa3Ej0DMwOj/Dr4gftx93EC0u3q65HzYOSL4rjZLMzZ0PTame04+o/9LvcA8O/lpOaV6RXuMPS1+UwEQAMQBNEBbv8Y+zj2W/qd5/Xhzesc6ezsevRA8Lbkj9xP2ZPPwcnf0MnQ79S23EjWlNrW2J7Vb+G/3HTfiufc4N3v+fSs9d3/3PjBBsUJYgm+GosSLyErLy81NVMRTCpdIGkqRPhe7z2cJXJCID1BS8hfB1R8RSooHBmRFgsGSgvMBMX9A/2u/yT86/Ye90/xAuaH5znc38x51LTeLOnk91YAgv3C7Zbo6utN5Brs2/bF9k/+JP/lBCIBx/8cAKf3ZvWU5+Xl8+f65lLyUPQf7bXpk9yP1VHSVsy8zDjS+9EK10PYKdkZ2NrWV99J3Njhy+FW5NfrhvBV/osBKvwPCSsB/QLnEgQPySDQKJczGkjKR0NZlVf3VN5T4UMdPGc7ED0zQeFTlGDgSL4+QjKWCwcP4Q0yBboOU/3yBDH/fPXhBb3v5e0D6LLZl94F0d7dXeRl5okAGPtq9H30Fd0G5aPob+k3++P4n/kvAWIA0gBGAFL6cvXV6ZXtHO144uHutfIP7XzxFuht2xzUscrD083Tns7i2WfTY9fm2/7ent6e2nXdbt6I4UDovO0u8/j+Tv/n/84FtfZCAJcK/wz4KfkkTzZgRFA4pV4nUKlNulSrOn86NjVAQepME1HcXrxSOjgfLQwRxgvKDxIRHRRZDLcGiQTaApT/EPz/7eri99qS3R7a4t3y6dztPPmeAbX2temG4GPY8uBG7Krz6fwE/Nf7mAHZ/Y76sPTZ7GXqfOWj6Prnk+xo7o3z3PEQ5PTeB9M2zKXRhtTR1onYGtuE37TecOPr4Qvacd503xvmAesq69n5/PXc+RcHf/nC+Y7+n/k3DwAcehuHQGM0NzMyW0I68k3TSYgybkPQK8BIwFCXSKtjgER0P8wtaBm4DRwRbxUxEp4W1hN5AEYIuQDM9Ur4VN7/5PnY+dj368XlJ+sY+sf7UvVb8v7h39Dq2nrjlvTRAQwA4QF09833XPmG8Brs4+fP4WXqXupz8Dj2leke82Hnk9wp4DvN+c/G3BDYROca63veZONy3e/kT+Er5r3n5uNV7rDvhvk1/hf0RP98+WTz3gWb+gkIdBoqG0Uy3S+XL7Y0LzGXP4Y9SDbVOewxjz3iRetKZkmSOUAslCN+FgwRjxg1DV8Zch1ED/4WNAbM+UoD1vQ89dnwqOWJ8cHuu/Lf/Wv++vehASD0AuZx6kXew+xi+lIAkgRH9z73cvWy9VP5uPZh64XlBe6V6SfwV/cG7XXxzevh5qzlntYm3Ljhzd/Y7lXiFd4l3eLYIOQa5Jnk0ON12dTibOWF5Vrz9+sV6gn1zesI7vP0bfBmA6oLTQ/KH7gVHRdrJu4ini7zNA8t+DH0M7k1yjtKPPw16DKoLucmSyJRIlUi1ya8LMQq9iTLGe4Rog0OCn4K+AkzAxUBngFw/Jr/xAJO+8D8gvY76lXlxOK75Y7q7vIh8yrw7+xv7Q3ssOt96+Pnb+Ue5o/p1ujA68Ds/+yV7njus+i15ZPk/eOw6OPszOwB65noaeh56TfrVe4m7HXqtOuF6Q7reO6B7r7yWvNJ8Y3wWe1g8NfzV/eH/0IBKQTFCSsKfg6jExUViRiJHIseMR8OIiUicSNAJBAkrCEnH3Id9R29H9QiZCcOJ+UlDSTgH6kcsxzhGtIZzRvvGP0TaBXVEboS3RP9D+EOPQeOAqgBC//4/iIB7f9iACIB5f4w/NX5cvZU89HyOvBc7VvqmOml6S7rnO157TDskOi+5pjlWOLt4zDkneNL5mXluuOD5LTip+PZ5CfjWuNI4obgweKB4q3jfOVj5OTmGufk5u/ohui86SbttO5/8Yb07/Wm+F38VgC2A70G7gm2Cw4OmxEhEwsWFhgDGNoaTRxyHeUhZCMnJy4q4ilaLLEqCCo8LfQqOix5LcIpMipMKYMoaCacJaMkMyDxHpwcWhfPFBkUrhF8EMYQeA30CtUIngagAwwA8v0T+efzdfLl7Vfr0+uc6cboROd641Df1tsV2afWQtUF1ZHSjtGO0TTPd876zoLNzs3XzrHNlsxFzXXNjs3wz1TSfdPZ1PPXn9m62k3ff+He4hPop+s37+3znfie/tQBBghUDmAPohUVGlEb4yAHI9wkzSe6JyMtGC6BL0g2CzP/NSc4/TNuNr00UzRjNCszJDMtMI8t8yylKY0n9yb1IfEeKRwKF+YUHxGuDtUMMglTCJsFjgFcAEP9OPqd+HD0o/Cc7Izp2+bv5J3jwuGT4F7e7dtF2VHW6dPB0sjRmdCv0PHOes7tzhTOwc7dzkfO1879zgDQONG10YfTLNUd1/nYo9vf3DrfF+Oo5bLpH+158Kfz+vf1/U4ByAVfDMoPxhSbGiUeOSFRIwsn7SegKBMt4i2/LTczgDReM/w1AzU+MyA0kDTgMzIy/DGgMH8txS0bK3cnXiZXI4kgZR3SGcUVrhGWEPgNugouChoHQwTQA7UBnv63+5D4CfXV8bHuvetS6Urn1Ob/5NHi7+D/3K7anNhk1kXVF9Mr0tXRONGr0SXRj9A50ADQWdAG0PbP2c9j0OjR3dMr1uPXv9jk2iXd994O4vbjJeZe6u/tOfGf9Rj6Jv2OAggJqw2xElQXHRspHWkgYCQ1IhUmsSo5KaQvzDETMAgy3DEYMx8xiTHBMw4vDzGeMgsugi3mLOQqJigTKEImsB8LHiQbYRYVFU4STQ//DOYLdwvjB9oG3wS/ANj+9/uQ+KP0lfG679jt1uwq64PoOOZW5KjhQd8p3a7ZMNi31u7VwtUJ1PPT0dIU00rT8dHu0VnQLNDL0YfSttNc1EvWqdhT2GzcId4N3M/hJ+MG5Ijqoeqb7iLyyfRf/gX+xQWEDvgNYBcuGvQaJR3bHlcj/iJwKMYs3iqEL7MwUzA2MbcvrTBkLwcwyTHPLUguoS5qK3YsmisPKaUleyIiIfYb7xiHF4QS6xHGEIsOiA0HC1sJFAc7BfoC6/5I+tH3n/Vo8vXxRvCP7cPsNOsC6U7mHuMG4Jjd7Nw6267Zedij177We9Yt19nTmtL90s/Qi9Gh0ofSJNNP1XnY0tgn22DcJ9sb3h/h2OGQ47TmzOgl6nTvWvOy9YT7pAJKBw4OUxSfFCQXVBvDHFUeqyLoJWEmkSvOLpUuWi/ULn4vFS4gMCkxIy1OL14usCyBLswsLivpKJ0nWSUCIaUdKBqeFrIU7RNOEsMPIQ65DBAL9QmkBnoDDwCj/Pj61PdZ9ZfzyPFm8efv9O4A7OnoXecW5M7iMOBR3q/cLdvy3DTaMdqF2a7VHdc81L3T0dKP0DPUC9Ja1w/ZdNZF3g3bmN3P4AjeluRg4HjmfuoP5aPwTu5S8Zb8dv3+Ba4KoA99E2oT3hm2GN4ZoCB4IeslRSrzK6MsgyyoLqItDSz/LfArYipmLXcrMSsmLSsrFCtyKesmqiP3HvIcCRkRFyEWSBLYEVYQaw6iDYALbwn4Bc0DggBg/Nj6Cvh89fb0evTB8hPx1++C7STr6egH55Tjy+Fo4efeF98R35XdEt2j2+fa39iE1lHW8tRs1DjVUdat11LZINwi3fjdcOB/4U/h+uLF5f/kHuct7Nrrvu4D9Wn5mP7DA+YLlQ0gD4AXLRQiFUwdlxuXH8UlAyhCKuUpGy49LPgpyC59K7Moais+KtkolSoLK9koqidWKIkkxiCUHzobdBchFqMUpRGqD+0OuQw3C0ALWAm5BO0CqQBD/L76Tfg19uD0SvOJ9EjyPfBd8GPscerI6WHmw+MI4jzh49+w3+bgRN/S3Ive5tyi2drbj9na1qzYeNmx2hHbWN4N4GDf0+R15Yzkw+cd6Gnokeq/7ULuje8E9+76Yf+NB6QKHw0CEcYT6hOvFdwYNRqBHqEi6yWdJ6ModCtOKhsreSwmKXYo8Cc0JxEnmCYaJ3Il/ySmJOUh+h8aHEkZARenExQTag+2DPIMegr7CT8J2wXcA8gAW/46/Bz5C/fG9A30gPRe8/jxzPC074PsN+u/6Vzl4OOz40HiOOI3437i4N9v4Rzhr93H3zXdWtus3Obcnd/G3NzgmuMk30PoAOh15dTqwuk066rrF+9G8Bvu3fcc+jr8vgVtB8gJjg3eEZsRVREpGMAXaBkmIbshniJFJq0nRCdAKLUpTicPJrgmlSUKJFYkcCQrImsiayIEHxgefRtsGKEWRxMLEu0P2QyzCzQK7AdLBusFUAOu/3v+wft6+N33m/YD9CXylfKc8eTuRO8f7SnpSemw6KjlVuQj5OHiAuK348DjcOAZ4ZHig+D54C7iSt+a387iQeMD5D/l5Oan53npfO0J7VDsBO/c8DXymfSt9/T3gfvLAQUF9gfKCrINRw9REhcXERfGFxMclR09H+4iTSMeIu0jJSZvJcwk+SS4Itgh5CIRI0YgDh7eHpcbpBqjG8QWABSzE84RzQ+FDbMLEwjrBVsGqQM5AEv/s/wo+q/5Hfit9OjxNfL88IHvffDv7e7qzuq46YTn/uXS5SDkF+Nc5ZDkIeM55NfjyuNw5A/leeRk4iLl8uXj5BnpnOhZ6OPr6ezb7mHv//Ck86XysfZ9+LX2BPy7/lgBiAXzB3oKeQyVEbIUxRUpGZ4aKhswHFkdNhzzG74eZx/2H9Mg7R93H70fNiBBH5YcpxtfGeoWjhbZFNQS1RFcERMQQQ7GDCsKHAjEBiIFJwK1/sn8Rfof+Tn5R/fR9pX1HfTw8xLyjPEm8cfvPu9J7RrrYukG6DDoU+jA6Dnpbegg6CDoBuiJ6Kbo3+iZ6OrnuegS6RXq3Oxu7p3wOPIb86TzpPM89fj1y/Zj+Cn5/vry/XgBlgM6B6UJAwx6D/0PhxM8FTQXQRuFGZgZDRdmFJgVPhaAGKMYZRm/GVwZzhkzGGAXURbiFMIUKxKKD2EOLA1LDqoOqw0gDOwIZwc0BgwErQITAOb81/ss+V73vvZ59ej2gvap9efzB+9G7err1OqU6//pXej650rnwOeg56Dn8Oft5wLpGOq46TjqnuqF6jrso+yN7IztRO+s8X/1N/g1+kX6Sfn++gT8fP2+AUsCtgNXB18IcQofDeYPbRO7FaQW3ReYFYgaVx/6G5gitxvyFPEWoA8uEuwQaxIFFVIR8hRKD0YMlA7hCg0LMwerAWX+WPrS/aUA5QA3A1ADO/8R/3v+r/n4+sz50ffb9l7zpfWD9AT7GAHr/hYAjPnH8/bwrO0e7o3snetO74PsJO+48UntX/HF8ffuiu/b6tfn4+ey6a/t5O998P7ygfJm9c37ovm1/ZT7U/l2/Q77UgBXAkUFFwsRDrwQLg7hDj8NUQ62FM0TxhQ7Fu0XrRscHcgmbyUeI7Ao1hQ/DakEwfbY/rj+WwZ+BXH/KAHZ+XL9nwAx+1D0aO4u4nDcJOO/4TXy0vnk/0oHQQK/CEEGngYYCRgBP/5f+fz4+/6eAZYMDQ+qD8YPFAY4/y/23u4q6LjlHuOB3vLhjuKu5jntSuzr7fHqPOkS6gPojezP6ELtY/QE92oD0wurEZAXORwlGocXixaND30PWBJ2EGsSYBP4EU4WShdtF8QWvhIbDvUMGQyJBL4J6QeCCNIQphDPDEsGcQLX86bwre+G5NPjh+eE5tno3vJG8Cfwkfe18dP0Sviw9LH6MP12ABAHLQsPEd4WyxmfGZsV7Q/RCTwE7AM+AqQCGAb4BSYIJgjZBPIAW/pC8mrrU+QN39jdXt4r4nDoc+xj8PHyn/EO8wjyb/E58QzxZvQU+Lr/4Qb5C14OYg2AC88INwcnBlQGIAfqAhUFcAMjAFUFVAbsB6YIDgrqAov+gwNO/8kDugupDFYIghGVEZYPpiA9IIkghhyvFAYImvyf/Wb1UPQ086bwUeYO56jqEuY87RntDO1l5e7mB/AP6fL1xgMi+hYEogzLBcQSnxzUGgkd7h79E8sJ0Q0VBXUBVQURAqj6WviK9xfw6PJb8onw9e396/Dni+Kh5mnlceqJ8QD4Vv3+AZ4GZAagBz0HBAY/BKEC4f/9+5X+K/+eAdEK8gzYDZgNTwhBAr/94PhT9CPxe+7M7Njt+/Ld967/iwWKB4UJDQfAA3f/TPnm9BPxK/Ng+AUBQAtkEikYhxcNE8AP+AmtBskEJgRCAcUAuQALAnIIdgyUDjEOBgj/AOH6Z/er9jX2H/Ua9Cb12fRy+QwAIAPUAusBLP5h92j2w/hW+Ov6u/44AR0DxwY8DP0LwgxqC3YELwC1/WD4gfct/M76jPlR/3H/CP77AeYDyAEMAGv/vPnD+HD4TPUE9+X1dvQr8xP1C/bL9or3jPX69xD0VfY9+Jb5Y/0E/2IElgThCf4JowtiDVMILgoiBXUFigNXAoQCEgEOAtQCtQT4BNUFpAIRAkv7uPpv+TD42Ppa+Kf7KPbs+HH7/vqVADv//ADC/W7/l/8E/9MDbABtA+kDIQYlCWgJxwozA2b9QPgn9F7ueufz527iZ+Nt7OTz7/1ZAN4FHQfrBSIFkgQtB1kAAABFATMAcQKICcsNCA4wELEOuwnYBeQBm/rD+P/1n/Xr9sL1Y/jw/Mv+awGHBmwEmAHvAH7+8vk6/MX+zP0TAz4GhghcDOYPZhBnDroLYwcuAjn9hvl49hr04POz9MP0F/ep+dH6yvvJ/Df8DvvK+yD8IPxJ/bH/wgDQAkEGQAczB9wIHAiBBukHgAcYBkcG9QTgAloDrQNbAVgBT/6r+iP50fZa9LXyoPDx7rfvKvBc8V30vPVR9z77+/4JAIYDzwQkAswEZwbeBXIIaQh5BD4CjwCu/oL+V//n+8H6tflD+KX6s/ys/Yv+Nf60++T7jvth+/L9Dv8SARYErASUBnkIFwcKBw4GEQL4/gb91fnm+Gz5qfgo+vT75vy7/vf/XAAsADv/gv7r/sv+cf84AVIBIAPCBGkEoQX0BbkDKgPUAVn9w/yS+b/1Rfai9Xz1tPcl+t37JP/bAawErQfCCMgJkglPCBYIdAapBFwE5AK8AHYA3f+p/Wb9xPsG+Vb5ivg79434VvkZ+XT7e/4DABwEGAZKB1sKxwoOCrsJCAlDCNQGWwWWBM3/QPwr+3b4+vfi+X73WvR89W7z2PJ094L5t/cG+d/5qfno/uYDlwZFCQwJ9wZnBy8I9QnGCwIJzgUaA2X+Ev7a/6IAOwLCAPz8ivvV+pL5vfv4/hT/1f3h/hj+L/40ArYDUwNvBHoDggALAdsBzgGdAtYDkwPwAjwEPQPlALUAtP8w/Nf7cfs9+Ar4U/ha94L5wPwM/vH/CAJEAh4CmAFiAU8A2/5m/Yr8CPvN+/D8LP5lATkEiAUdBwMIxAZ1BZkDqAEB/438YvrQ+Ib4zPnh+mf8X/7u/sr/WAEnAm4CNwI1AY8AZgDfAGEC2QO1BDsFBQWyBKwELwRjA5EC7ACF/kb9WvzU+5D8Sf0M/uv+Qf9X//H/aQBpAC8A5/8AAPT//f/PAG4BhAIWBHIFtQVyBSwEOwLsAOr/eP6s/RP9Kvz3+9T7lv0b/38AWwFEAicCigKnApQCMwP3AocCKwFCAXUB0gA1AZECFwJ+Ai0DigJQA9kDHgKYAfQBiv8//qL9yvux+5f7C/sE/Gz9mvzj/Kv+0f+FAXQCagKaAlQCYgHUAe4BYgHMAGf/Af/E/5IArgHtAk0DLQPHAu4BXgH0/zj+5vxx+wj7i/pY+tT7/Pwl/q8AxALNA1sF4QWoBWQGegZoBV8EkwObASkAyv/K/3H/Ef8M/tn8qvzD/Dz9vP3c/dX9TP05/dH+PAD1AMUBiAEIAdsBTgKXAtMDtgPQAoECNwL7AdgBvwCx/wj/r/3D/Cr8lPtn+9f7hvys/VT/xQDeAQoDaQTsBBUFKAV2BIYDFwNxAsgBCALrAbEBAQIUAvEBVAIRAg8BXwDY/lb9ufzq+2r7d/t6++37ef1L/ygBRwOyBOQF4QZQB7MHmge3BogFBgRLAlsBTwAR/5L+3P05/V/9fP1f/Yn9ef0p/aL9ov15/fL9//27/gAA3wBBAnoDxgOMBBUFGAWlBXUFaQSjA4cC8gBpAGT/W/6m/bn8APxX/Kr8Gf38/Y7+Qf9MAEUBIQL6AjADPQMTA+QCvQKdAi4CuAE7AdwAggApAOf/vv8o/4v+Ff7C/Vn9Fv0J/Sn9zP21/qf/mQCFAU4CJwPzA1wETwTcAwADFwI1AVkAgf+b/u/9hv1//fz9tf6a/08ADwHFAWcCHQNWA0MD5AJuAv4BTgHcAHYA5/+a/6r/qv/N/y8ADwD3/yYA+v8GABYATv8r/yH/aP4e/67/O/8fABkAof/CAOgAfwChAS8BJgDPAAMApP+lAC8A5/+cAD8AGQCcAE8AXwB2AO3/uv87/8H+ov6V/qX+If/h/wkAwgBCAWgBKwKRAnECVwKLAYIAwf8k/9j+vv6C/kj+pf6+/qr/uQB1AaoChAKdAvQCpALBAmoCJAKoAc8ARgAWANf/yv+iAGkAZgAlAaUAsgA1Aa8AeQBcAGH/Vf4o/uz9L/7b/sr/cgD/ABQCEwMJBN8EBQX1BE8EAAPkAeIAjv+b/n/9Tfy0+0T7+Po7+5H7APyQ/OD8Y/1F/qv+5/+oAYEC6QMlBQgFAgVIBcUEzwSJBNcCIgEU/z/9HP0w/fz9rv5P/vL9LP64/kYAmgLjAzMEmQMLAvgApQCFAJsBNAJiAYkAMf/J/YX+OQDhAcMDigNIAVT/0Py9+/z85vyx+7X6Z/dJ9Sv3WfmJ/fQBvQM8BGIE6QMCBSQHZwcABwwFwgDc/Uf8Ufv8/BX+Qv6o/lv+zP1e/9wALgLzA2AD3gFcAG/+gv1l/gT/VP/K/5r/qv/MAEECbwTVBZ4FvwTgAtgAgf9P/lP9zfyR+5v6hPvA/Cj+rwC3AoADZgTMBPYDFgSKA0cCBQEmAPT/p/+k//wAVwI0Ak0DQwQnA+0CagIAABj+w/xi+vD4P/lW+Rz6cfuz/NX+tQDBAlUFkQakBgAHoQWQA1MDPwG+/xMAov42/Xb9cPzN+5L9pv2s/Wf/Y/0Q/Hb8MfuE+xn9E/1G/Qj/qf1U/3oCMQIFBVcGOQTsBEYEpQFTA60D/gHTA40DdQF+An4BLwBFAar/CP5W/Sv7Nfr7+u76DP2a//f/QQLiBAwFkQaNB/4FMgXwAvr/X/2i+iP4cff49if3KflS+pr7GP5CALUBzQOJBCADkQHK/7/9B/z6+6T70fuK/Pz9jAD6AoEG8gjVCVIJ4AeeBfoCQgER/0z9+Ppq+Ij2P/UA9QH3H/k4+sT70Py2/NL95//FAP4BuAGxAYoCRwNqAwQGkgX4BckI6AmqC7kMZgxfCaEGEwNGBNEBdv31/kr8S/bs+fH+Vv3rAfoG5AWtBwwMvg3aDkAPSw4mDAgGLgIcAOb4t/P689DwD+4O8z/1MPgGAI4FdQk+Dv8QtxIHEwgO3wzHBo77Hvd29C/ts+yi7eLpRu198CvzAvpeATMDqgfJCH8IbgqyCKcGDAVBAtr/Pv+g/BH/WP5B+2L+DwDN+/z8uPrW8H3vK+4W6d/pvO3N62DscvGO94/9/AdEEpUVwhl9H7of4R4lIkMg7ho1Fs8QeQg7AkX+0fdK8xfwK+oi5vjlKeVq59HqL+3A7w/yYPTc+D7/SwUYCV8NRRGEEtMT+xUbFtATOBEoDZsJZAadA/v+6fzR+wr4JPd9+BL28PMG9Wrz0/Rk+/X9MwBUBmUJ9ArmDx0UiBWrFkYU5A6jCzwI2AFm/WT78fb78h/yJ/Nq9Ir4KvzA/A7/zQJYAXcCxgfnBh8EKwIC/kn5KviI9on0HvMz9FLyMPAq9EL2R/f2/LEBYwMCCXsJNAbOBYUFLQPLAY4BUgHB/qr7iPo5+Sn5g/iY9tb0bfQ+8+jxc/AT8G7udeoj6Anpk+z28OX1ef1UBmQOoxT6G+cjXCmyKUooXCGXF/4NOgM8/c76q/YO7xLpRuin6xLyLP1VBfQFzgU+Bt8IMRKQHHcbfxCYCQ8Bhvlq/CkAXvvk9pn0EPSa/CsK/hXaGpgeFiC3HjUaEBQeCtP8TfAa5DHbzNQp0LbLO84L2qHqbvs0C8wUSRlJINcjIiZQLKgqXh6+EtkI5f3P+Rf8ifkW+bz5wPPC8Z/5h/uZ+Y76CfXT6xXqkulo4RLiWeUx4i7nCfH/8LHzef31/WIECg9mDGYE3AOO/4P4BfrI+jXy8Oup6MTeJtzT3yrfcOPf6LzoNu3I9oIAYA/dI9AwczmJQdNAhT7kP2U6lTEoKkEanwht/LjyyOmU5oXluOGQ4zLmg+jR7uj18PvlBM0HmgqbDUAMJA/dE/ESDhZmHA0cpR1yJTonwx+pIE4aogxwA533I+Q42uLU4symy4/MC87b1THi0/CZBBYYDCX+LlA4azt2PbI6RDMmJXwU+AHY8ULpXuIz3LrXvtmu2Snkv+0A9UH7kv7p/Bf4mfhG9ZHvXedC5Tnhq+Gf6dvut+8U71HzxfVyABwR7RKHCrQCffN35/LlaeXK2unUE8wavjTCAdJd4530EQrpHEUqgTutSMBQf1KBU3dMC0MgORQjkg2u+gbtmehb6jfr6egq6KHmTOlP8fz8UwTjA+QCOvw/9i/2lvhw/HwA4QWECswQghn/JDMsYDSPOYM4hjWlKrwc6Q+eBXz5ifAF5n3bwdJbzrrO0dKu3kTnRvHB+uUAXgYHD/8UWheoGV4WnxBqD1MMqwnHCgkMawqKC88IcgQlBRAEqgIE/xP1x+va3kHS0ckqw5O/77ySvA6+pMI3y7XZtenY9lIFIwwBE1wcqSD+IbYgeBZHCp8Epvgf7R3sFeIX0wXRndOX1kHmkPjVBO0ONR1CJfww7zm+O4I56zbFLTwh8xsPEZYEyAG5A1X6TvvgAzIBaAEqC7YLqwnCEMYMdwJ3AugAofZD9fj57/GN75b9uQR3CpQb2yVPJbksxjQsLRcr7irCHS4OhAZR+5ztpe3q67PjCOL+5bPjYeMS7qzxRfIh91f72fl1+mkAXwD1BN8MDw2TDwYUMBPkEmsWnxQoEQsOSAXx+kLyAety5cnguNlV0SfLMcYuwgjFicTFxMbDTsZBys3PrtoZ6RvuN/jXAjsG3QtcFGATERMDGCcPAAhiAfz4iu/f6LfndeUV2obcgulh7/sByCGuKgI2v0XmQGc/FUvCReQ3yTBTIFsGZv0r/+vyavCr/gAAZf7GEJgZVxNYHi0kXRtvHasZBAYd/P33JOtl6UzxQ/BI8pn9SwUFEjMkozDJNDQ7hjn0MtArWCIFEjcHU/3M8V/pouVp5Y/lKucj7J7y6PF88Z/1YvVp8fvyJ/R78q/xrPHZ9AP5KP6CBTMM+Q+fFNwUKxJEDjYMzwRsAC/2SeSI2VzQb8jVxDHG8sDaunu5LbvSwGrLmtcp4MXhMun76a7u7ftdA3YEPQt3DloLTg42DEcHEgU4BX8EG/9h98HyNOuy6WrwpfXH+60CqwEAC/gZHCUnM3c/Mj4nNAQwnip3J4kkPRw2EPwHSwJt/HIBaAl6CkQO6RirGZUZ8x+BHoIVhhTKElkMSAVaAwj6vPUr92z5nADHCr4OJRGTF1Ud9B7eIi4nBiW1HUAXJA+VBQj/bP0A/H/5sfZP9Vn1MvJc9cn4h/d49rP0SuxC6gHrEuX65intFukO6uXxL/Wb9uz91AJuAqkDKQQpAGb5BPdy8czs0+ga42fbPtbi0GPQBNJaz8TOuc8syHDHitKy0dvZd+PC5R7idekA8BrwI/1WA84FWAXnBi8Fmv9uAbsBvv5e/4T70v1i9hf00gDXAisFfRN/He8YRiDpLM8pYjEZOe0vpyfzI/sZvBRTGAMUqw2WEOEOXg4pFUUd+x0RI3onFiU+Iqkg/hW1EdAPJgiwA3YEk/y19mT3EP0RAqUJkxPeFkcXhRnWHDkdBB94HZEaVBNqCz0DuwGy/Wv67/2eASv7k/y4/tb83PjF+k/5C/I662Hrd+NS3Wvi8uCD3Ovl2+WT5KHyt/PB8qf/5f5m+Pb8d/us8BDw0O+a523n1uT44dHewdo+3oXZp9r83GvVzdIx0lPP0coT1A3TENBW3BzdtuCM6WTvvPGN+BT/R/9fCEwIjQOoCdgBqAHbAWIEHgZaBxAHVweLCTAHIxA2HPAXPSTYKr4i8SpiMX4v9yvoLcwkRxvzG7oXkhmJEEATBRWfDBQXJx+aHwogaSQkJ0AgySA+HgQbTBABDiQOHgKl/oX+NfZB94z9OAFSADgJmgvlCOIUUBgNF4Ud/xyvFeoSdhQeDvkLRwsoBdcCv/1o+mz5evdt9FbxyO7f6ATnr+V14VDfTePi3LfeLeMX34Pk8+gi5pzo6fBm7JvqFPPC7ezonesq703nweaP7angQd+Y4uDfqtv+*****************************+D+4cjqq+aB7oPsxfFP8SjyqfiF8ZT36PXU+0P9jwAoCUUF7Ay/DF0PoQ6WDz8VLhJiFcsazhrqFg4fNyMIIqMslytRJ3QjrSMCIRYdFCPZHHcb7hUxFp0XMRZOHpkc/yDHH8kg6yFTHLIh5B4KHzMc9BYqExkIOQh2A9wDbgLO/j8ANPuF/jQCwwOsBJ4J3Qs7Cc0LxgwMDJoKsQoQCxgKlgdbBTcGQQJ8BN3/MgFe/zn59/su99Dzbe/n75HqTub05nngOOKN36Pg8+No4kHmXeMq5wPoSeXj6FXlI+SU4j/hWeCP3THegt2u2ozhyt+Z5M/hWOJR5rfjb+kz6AroO+Y35/7qfuaU6xnpcuky6kLqf+3Y6bzsLext78DvefHg8x30vvax97f7zPlX/FT7yPpS/nUBRf5n/9ADmv8uArgNiA20CkoPNBMaEyQb0yDbHgYcnx0JIY8hAyCkHkkZwRZYFqcXBBeKExUVNBeUFmsa9BopHAIdMR5FHkgePRwcGZcXDBVzEGcPkgxeBuoG5QR+Au0C3wTVBSkE7Ah4CXcGiQgOCjgJ9QQEB4sBjP0T/T/99Prg+O372/ot/CUBLwGGA8gBAwTvAGIA8f5W+UT3PvIu70DswOvb6kboJeqB6//ss/BU8/3zJ/Mo9m34rPXM9czw6u/36xrs2Omc5PLkbOQy5vvpz+gM7dPsk/SR9/T3EPzk9vD7Bvn99w34sPAO843rHO0h6xvqmuvQ6yLtl+/M7crzofY59UL2Nfbg9KH2OPpp+af3O/Lr+pz1tQE0+yj/wPys/RAIXwR2CGEGpAbyCK8ImgtOBrQGUQbUBtUMTwgFDa8Mgg0CEaoSSRQ0D68QCREFDhQTpgy7DfALqA1XD0gOgxCDDO0L6A2MDAwMdwo4CkwIXgpJDIoLaQznDisOeRA0EjAQARI0D9UMsgypCIgFgAOEAlsBNgA0AjgB0AOkBsIIHQsXC5UNNgxTCxUNswfZB50C7wCx/9D8rfz0+gX63fuJ/UQC7AAcBJMD+ATsBMkHrQJIAaL+tf1X/GP9KPpq/NX5T/5FATUBXAQ1ATADZAJUBhYE1/9CAbn9bP1u/7n8W/oI+r38jfwY/kP9b/1i/h8AZgAI/4b9AP1D/dX+BPuf/Sv6Dfy1/qL+GAGH/+gA2AF6Ax4C+v8vALX9ov1r/vD7NPur+uD8pPsI/qn97fu3/yT/rAByAff/ywHE/20DdgACAV7/GP/8/cz9Jv3e+mf7B/xX/MX9q/5WAC8BsAMlBZgF2AX0BuUECwatAucCKQBu/5L+DP6V/tf/WQBzA0ME4QZFCUUJagvKCoEKdwrQB90G/AMgA8sBFQFSAVIA4QFKA9kEJAegB88IPwl+Ch4KHwn2BycG0gSwA4QC2ACxAf8AjQMfBL4FagfJB6IJywmOCckIegfuBbUEBAPuAakA6v9mAA8BagIsBPgEMAd2B28JfgkICdkIrQabBSwE3QIIAZkAR//0//T/0QFLARAEigM3BrUFxAYrBi8FFAZgA0MEKwFLARYAHADt/4IAfwA0AnQCiQSiBPEF9AX4BSQGTwW8BDMEdAJ0At8ALwGPALUA/AACAZoCBAMsBBkE/AS/BGsFWQQpBA0DTgIOAgIB7wA/AGkApQBoAdEBpAIXA6kDbwRMBHwEAASdAyADigLrAT8B0gCiAI8A8gACAcsBQQL6AukD8AOiBFYEggQZBJADOgN+AugBdQEvAfIAGAEfAYsB9AFqAscCCgMdA0cDEwMHA6ECQQLoAYUBUgEVARIBOwFSAasBAQIUAksCYQJOAmoCLgIOAsEBdQElAfwA0gDIANUA7wArAUgBsQHBAdsBCwL4ASEC4QHRAYgBcgFoATgBOwE4ATgBdQGRAcgB3gHFAdEByAHBAZUBPwE7AQIB+ADiAM8AyADVABIBIgFeAYUBhQG1Aa4BqwF1AV4BSwEIAR8B2AC/ALUAvwDlAO8A/AD1ADIBUgFCAUsBGwH4AAUBrAC5AHwAUgCSAGkAggBpAIkAiQClAN8AyADiAKwAvACcAJ8AZgBcAFwAKQA2ABMAJgA5ADYAVgA8ACYATwBGAEYAZgAzAC8ANgApADYALwD9/+r/DwAAAAkA/f8AAPr/AAADAP3/AwDx/+f/6v/a/93/2v/N/8f/yv/E/8T/t/+x/7r/uv+x/57/of+a/57/lP+h/5H/h/+R/4H/gf9u/3H/ZP9r/17/S/9e/zH/O/8h/xv/If8e/yH/K/8Y/xj/FP8I/wv/+/7o/tj+zv7L/rL+sv7B/p7+rv6i/pv+lf57/oX+i/6V/nj+fv54/mL+b/54/nL+Yv5f/l/+W/5Y/kL+WP4//jj+Vf5b/kL+OP5C/kv+Nf5C/iX+Mv5C/jL+KP44/jv+O/5F/kj+T/5L/j/+W/5b/mv+Zf5y/oL+fv6F/nj+gv6O/pL+nv6e/qX+pf6r/rj+u/6y/s7+xf7O/uH+3v7h/tv+6/74/vX++P7u/vv+BP8E/xv/G/8R/w7/Mf8k/y7/NP8k/zT/Mf8k/zT/Pv8+/y7/OP9B/0v/O/80/yv/O/8x/zv/O/87/zH/O/8x/zj/OP8e/y7/If8k/yH/GP8k/yH/JP8b/xv/GP8B/wT/+/4L/xv/Ef/+/uX++P7u/v7++P7o/uX+Af/r/uX+9f7r/uX+5f7r/uv+6/7o/t7+5f7e/tj+0f7e/uv+5f7u/uH+2P7h/u7+3v7l/tv+5f71/v7+6/77/u7+6/74/vj+7v7+/gT/+/4B/wj//v77/hj/Dv8e/xH/GP8R/yT/KP8b/yv/OP84/y7/NP8x/zj/Mf84/zH/NP9H/zH/O/8+/0v/R/9B/1H/V/9U/2H/Xv9R/0T/Xv9h/3H/cf9b/1T/bv9u/37/gf+B/4H/gf97/37/e/97/3T/e/9+/4H/e/+B/37/hP97/4f/iv+H/4H/h/+B/4H/jv+K/4r/fv+a/47/gf9+/37/jv9+/4r/iv+E/4f/jv+R/4f/hP+E/4T/hP+K/4H/e/+H/3v/Z/93/4H/dP9x/27/d/90/37/e/93/27/cf9h/2H/a/9u/2T/Z/9n/1f/Yf9R/2T/VP9e/1f/V/9n/1f/W/9U/1f/Tv9H/07/Tv9R/07/Tv9E/0H/S/9L/07/Tv9U/1T/RP9B/0T/S/9O/1v/Qf9R/0H/Qf9H/1v/RP9k/17/VP9u/27/Yf9u/3f/d/9n/2v/dP97/3f/e/+H/47/e/+E/3v/d/+O/4H/hP+R/47/kf+U/67/jv+U/5f/mv+e/5H/mv+x/77/sf+h/7H/rv+q/7f/uv/K/77/x//B/7f/t/+0/8f/wf/H/83/xP+x/67/t/+3/83/vv/E/7H/xP+x/7r/vv+u/77/xP/B/7r/t/+0/6r/p/+q/7f/tP+3/6f/tP+k/7T/qv+a/5r/pP+h/6H/nv+K/5f/p/+h/5f/kf+U/47/l/+X/5T/lP+R/5H/iv+R/6T/kf+X/37/jv97/37/gf9+/4H/fv90/3f/gf93/4H/dP+H/4T/cf+B/3f/dP9r/4H/hP9u/37/h/+K/47/kf+K/3v/gf+R/6f/lP+K/5H/l/+K/4r/jv+K/5T/kf+U/5H/kf+U/47/kf+e/6f/nv+k/5T/nv+e/6H/lP+k/7H/tP+u/67/sf+n/7T/tP+6/7T/rv+0/6f/sf+x/7H/t/+x/67/uv+k/77/uv+q/6f/p/+u/6f/rv+u/7f/sf/B/7H/rv+6/7H/pP+k/7T/qv+a/6H/qv+n/6T/p/+k/7H/of+X/47/iv+O/5H/hP+R/5f/jv+O/4H/h/+H/4T/e/97/4r/dP97/27/a/97/3f/cf9x/3H/dP9k/2v/bv90/3H/Xv9e/17/ZP9n/1v/V/9h/2f/W/9e/1v/Xv9e/2H/V/9b/2f/Z/9b/2T/Yf9r/2T/Xv9n/2v/Z/9k/2H/Yf9n/2H/ZP9n/27/Yf9U/1v/a/9h/3H/dP9h/1f/d/9n/3T/dP9x/2v/dP9u/2v/Z/9k/3T/a/93/2v/cf+B/4H/dP93/3f/d/93/3f/dP9+/4T/fv97/37/fv97/3f/d/9+/4r/e/+E/4H/fv93/3T/cf+B/4H/d/+B/4T/gf+B/4H/fv+B/3v/hP97/4H/e/90/4H/fv97/3v/d/97/27/bv9r/3v/d/9x/3f/fv90/3T/d/9x/4T/e/9+/2v/bv+E/2v/bv9n/3f/cf9e/27/d/9u/27/a/9x/3H/bv9u/27/bv9n/3H/a/97/3f/a/9x/2f/ZP9h/27/a/9n/3H/fv90/2T/cf9r/27/dP90/27/a/9+/3H/cf90/3T/cf90/4H/fv97/3T/dP97/3v/e/9+/37/e/+B/3f/gf+E/5H/jv+H/5H/mv+X/4r/l/+X/5f/kf+O/5T/jv+O/47/lP+U/5f/kf+O/4r/l/+K/5f/l/+U/6H/lP+R/47/kf+X/6f/lP+U/5r/jv+O/47/kf+R/57/nv+X/5r/mv+a/57/mv+U/5H/nv+U/5H/mv+X/6f/pP+n/5r/of+a/57/pP+n/57/l/+a/5f/of+a/67/of+R/4f/of+X/5r/nv+U/6H/of+n/57/mv+X/5H/lP+R/5H/l/+h/6f/pP+h/5r/mv+n/6r/pP+k/5T/iv+X/6T/l/+u/5r/of+k/6T/of+h/57/lP+e/6H/p/+h/6T/rv+u/67/sf+q/7f/p/+h/5H/of+h/6r/p/+h/57/qv+h/6T/p/+n/6H/p/+q/6T/nv+a/57/qv+q/7r/t/+3/6r/vv+6/7r/t/+0/7H/qv+h/6T/of+h/7H/sf+6/6r/p/+a/5f/pP+x/7r/p/+k/5f/p/+h/7H/rv+k/6H/t/+q/6H/pP+h/6f/qv+0/6r/pP+k/5T/nv+q/6f/pP+u/6r/qv+q/6T/qv+e/67/p/+3/6T/rv++/77/p/+u/6r/tP++/7r/rv+u/67/p/+6/7f/sf+x/7H/tP/B/8T/sf+u/6r/pP+e/6r/p/++/7r/t/+x/6r/rv+6/77/tP/B/8T/wf+3/67/t/+q/7T/tP+3/7H/p/+u/77/sf/B/7r/t//H/8T/0f/B/8f/0f/d//H/zf/K/6f/rv/K/8r/vv/H/9H/vv+x/6r/vv/B/8f/yv/K/9T/1P/X/8f/x//H/8f/x//B/7f/tP+x/7r/wf++/7T/uv++/77/t/+6/8H/x//a/9f/2v/K/7r/sf++/+H/x//U/77/x//H/8r/t//K/93/3f+0/7r/4f/a/8r/wf/X/9H/zf++/8T/wf+n/6T/p//B/9H/zf+q/5H/t/+3/8f/t/+q/5H/nv+R/6H/sf+q/6f/yv/B/8T/vv+6/67/tP+3/77/t/+U/47/sf+u/5T/jv+e/6r/pP+x/6H/of+u/5T/pP+k/7r/qv+X/5f/nv9+/1v/ZP9k/07/S/8+/1H/Yf9r/2T/Z/9+/4H/gf9+/4T/d/+E/2v/RP87/w7/7v7e/sv+vv6Y/oj+m/6u/rX+rv7h/tv+C/8h/yH/Mf8u/zH/KP8x/xj/KP8b/xH/+/71/sv+ov5+/m/+S/5l/kj+eP7F/tX+4f7R/t7+3v4b//j+5f7V/tv+C//1/kT/Yf+k/4f/W/9X/y7/7v7h/s7+0f7L/sj+2/7+/hH/C/8h//X+Dv/r/sj+q/7V/uj+9f7x/r7+zv6u/pj+fv57/mX+df4//kj+Vf5i/pv+sv61/u7+Dv8b/yH/VP9O/zT/Lv/h/sH+eP5V/uX9yf0J/b38evwq/BD8Pfzt/HD8fP1m/Vj+Mv4r/7X+G/94/o7+TP3p/cn8+fyQ/JP8/Pxn/Gn9+vsz/QD9Rf4B/1H/Lv9b/38ADADiAMgA5P/6/+T/x/80/wT/Ff5V/o/9M/2J/Vf86vty+nv6DPq5+X/5dvkY+kX6zvog/MH71vyv/UL+2/6b/t3/vP3u/un8eP51/hj+RP/j/Oz9tf0c/YD8Zv0O/8D8If/V/gf8yP6C/iT/ov3b/v76CPtj/MP8fv+vAHIBNgAqA7QCxwb3ApP8X/0F/nn99fnw+17zvfjh+w7/Lvs1/uQBrATKCrIMiwU/CT8JxgP0Bof/zfum/PX5lfWc8ev6nPHP/cH+WP5CAE4KBAZ6B2ATIg0FCVATSgdb/mcCxwKQ+F/6kfYo7j3wN+/W7Fjy1fV88Wv+mQAvBGkI/RNXC6oHFRI/AdACHgr091z5QfeS+qzwNfo7+7nwafWyAGHvxf5D+T7ylf6uBd4RtvzYDesNQBd7GiUZavzLAa3z4/yF7m7edd2xznIlQQIsHboXcjX4MutGOjzBJqYkiCKXEs0GQ/SC2d/cv9yp1EvaWdB935XxfPUjBNcKuhtUHy4zjTACJvUp8RI/DSsBQvE/6b/c8uU015rjZ9Zh51Trj/VZ8d0GbgUUE1IRgxwGCO4adRWJFH0TUAf6BrkE6AGfBBv/i/bTB8IAtQXLCasB/g0yBbIIjAyFBYf//gGt75XtzPSn55HyIe9X//z1rAgTFOQGYxBlEc0HAgklDazxl+9675/cS9b02nzgcNPw70YIMPhjGC0kDieUH+wtOiT9ChYk+Ag09xvyLvNG6MvhA/R/3Abpuf07CuESAwTDGFMYOhOxElHvJPel4Z3vMdLNz4jZzNgFHoYHpiBQKDYtNlJOV7VOF0BvPn0o6BGzD6rbReahypPgbs4zw2PcattE79/5jBgg+NsdnSiRJlYsDh4bImwVjRf1DbzkYOT82CLZENDQ11TbNepZ/U0cUiloMqJG7WCIUj5bRkntQJs2NSohFlj69uiu6u/QgtHVyE7SrdeV6WL5gwypDLsijy3xJrAwmiv2JFMg1xIIDsr/F/yo7jbszvYG6aTvL/Zk+3oCHhbjIEkg5iAyNukkbxX3IjgWBAcKB9T/kvX+5o7+SPJo7mP4tO4h/8j69Pq79nr4cfel9rDvq+qx3kXd09/m05DXYNe5zxLZaOKn33DoBPcw9F4BNQXtD2wERRF0Ah8RVgjc+G341+fU+0HneP4i5rfv5ALg/Nz9sv4w/BIBcvY5BJz1fvcY+4734PSq7y309+ci+pvqT/He8qfzRwJHA7X1OhMHB3QKHBUq+C8RO/oABH0Dofbi+eDrOvxe+9/5qf30CvL9GAaKByr8hPsWAAn9awHD/BESOP7YFYkUwhSVHUQTmhv1HVIZnxWuDYcKsv2e+mPw3+yQ6Kvhsul16XT3JwPJBBcHVhQmGGcbrBgaKNsR9hghEw0QhQm/BAsWFfa2Bw/+EAt1+noHKheJBNcKzQaKAuj5SwEj8Gfza+pw6BXqWu9u6gvzd/uh6rH/xO8/+oX2ifFSAWzpq/4O96ALJAvbBYEjowdAE64KfASOCpX2QgFB+67uZQGy7dfzkfN3BsX5zAQ1CUwM4BeQD3gizhFKE/4JygZP/Xr39twl4sLUP9ntz7PLTeMj1I7uK/fcA1sG2hfOHs4WUimBIkEbZw51Daz5S+oB80ja2t4C5XrfderV8ZL6kv5IAYkkZAp1EbgirwhfFSkAlgQeAnf3Ivbj6wD1PfDr8o73M/kvAPACjgX8AOr/vQPa5/H6T/m25BTneudy7avhzuLl6UfnSuzf7V7y1vh687UFNgxzFNYLagtjGLsVLRudBzoT/wRpDEcLpPOpBG34GQyLATD0ovXA9OgAQgUr88z9TfwyARgKs/hDBJ/9uwXU8wj3DPn06p76H+my7a3jNOOMABntGfn1/eADcg1hDncSMBNgF/MTnAz6AgoDBf6V8loDOfEh6273KvSk+0kAuvPJ+B8JnQ9NB0cHDg4XD2YMegeMBOn0CwK9+J7/0PON8/P86PmKBp3zkAOm/fQK6f0vAGYMU/0qD2gFG/bMCOz1KfUIAqPwLv+t788AUAPTB28ZBQlhHjQbxg8tF18R/Q+AE6QKqAnJCDwULRPhBiUNKQCwA/j5s/ht9FL1kfP+7hLyoPDY8rHqtPdn8wX69+oz8ND8aPIH/KX60funCosOBAvJGC4KABSWEMkQ/BSGAxL+GAF/9Sb9ZvhO68ztM+yS7TjuoPTz+F3nQ+hC8qv2wfZy8hT4YQJq9EIAcv6oAaT3hPuiABQGhfZW+Y/xz/UH8+rvnvar7tvqKuzY9b7ufvdq7+DwtgPH/83/pwod7JYDffz1+UwE+AQi8hP1tfkh7yjiPvsZ3IX2PvI55ED8T+019vPwqfUyAZrz2/4Q9E3wz/Hk9rPb2PV56UDX1OYK43Xd0+Nk3vjRut8r1grg0erH54LtgfM46rf/AOx69/D4ffDfAOXtKPbK/2frPu8L/2D4G/NEAl/69PbnClLy//0//aLtIwTUBlLyKf3Z+EXyo/iv9Yn0Y/CS/dvyEfsD9cX13fOt7/z0lfXb+uH2V/PaCg0Dl/u6C+kDtwLH/+gFswcmBFcGdgD2B6j2W//0BtEF8PwBAgIJ2/Jq+LwIVvnh/wYI2ABvDJoG3QuZCE0PjQvLEe0KeBaWCGsFcwMA/QEG0/yG9CEG3vamDI0PyA17Gr4WpB6GGAkY1C/YGcgRlSWmEJUZbQ/nFvgN2hKFCasZpg/HBr4NIAzvCNv+Qwvr/ksBAwgU/BQLmBGyCHsOSA3qDjAHOhtcBKINIg0S9v4Fevic+fcC7fe6BmsF7wQQEDoTjAgxF3MTgBMnE238HRN0CnIAVvg8/TP1gu0k/7XxsfZj/Ub5J/vVHWMILhqvFFcKMRsbDu4RlQFZCMX9D/r687rz+fiG8JP4h/cR/+gEUftKC3D82An0En7+5w5VAaII1g8i/iwAWPaAB63z9AWJBPf3fv75C7wEKxaeCbMDKhMkByUJ8weWC9/9Pwi7+mUFiA1z/N3/DQNMAFL+CPtTAw/ubwQc+s3/EvoT9M4Fbe+H+9H7KP/0+7n4cAus/d4F5P+tByMApgtFEbINnRdr/38Rqgop/aQO4/iKAsH7yfh0ArLxbwA9/Fz5SPoMAPz4cPyqAmL6PgK9/Mj+RPsc+YX2Mv5X+z/1svXR+3kAbgU3AuMDnBC5A6cSMBNaD5gRiA1bBpYQGf20/7EFGPYB+27z+fj57EfzPPFe7tn8h/tx8+YDOP9V/jQCogBgA3r7DwVqBqcGxgd/DKoLaw46D2wl7BTYEQUVXBkyEYsarBAPEYcWBwfpDyQKsv7cCNf/jgH8B8EFHAixChESBRWbEk0X6Qx/EGobdAZQENAPUweNCwAMow8ZCL0G+wUnB4f/7xCkBlsGtwYtC4oPBfqjDz0PIQZ5EMUJjxGMCH4BWw2ACxQLaAWkAlsS3wCuDoYHZ/yBAnPwVgO6+2v63/lO4rr7Wu9R+xH/vPBU91T3ef3V/iv77Acs/rn9Xvtf9X4FDvMkBw7zN/RTA1LxiPID9Iv2N/e86YT/W/5h6vQFqvs++3b1U/BrBe7mDvfh/nPwPve19Xf7CAHH9/L1FPvn89H/1vio+qn5DPFv/sUAa/I6/MH6X/1z/EP4fgGf+YL9N/sr+pX1T/Vj+Cj66/bx9sn07ecT/XrzzPWxAZf3mfGs8ar7YgAZAPkLIfsp/S77hP8pDBf4n/3m/NjtEftH73f7uPYe7073Z+9p/Rv3UfP1+Wb4Bvl+/2/5BQH0/7f7TQtR/wQDtf5T/Lz5mfHp9AXur+RV6fXp3+yr3jLmfuqY4mbsM+RW+GfnovqN/IL5dAYg/OgB/fv3DjP8HfgX+F73cvHk7/TzSfFp7J/tuvca7GL10/AD9Gj2puzg+G7yYe4XAmPw6Plr9vcGevtO8yUFlQCd/Lb82f2S/u/9GPs8CLT/yP7n/9j+PAA+++wA3vZaAgD4L/qd+ELySwFi9roGIwBS9YgBuf2UAvQBAPgGCJD8hAI/+gX+XQ8/+YoHyv/cACX65fn79nfzrfiA6Aj2P/VB64Hyb/Eq9DD4NvXe5rH39PfU9y3wnu6C/jPwE/1gA5H3cv3s+dQC8Px6A70Cvfj696723P3+8ozxyPql7ePjPO0k82TvZPMC5kjuw/C+7tj6//0o/jbx4f6N/OMDUAO9/PL90v0v+Zb5C/PK8wf8beu1+Rj2vu4e+yvzwQHe+rQCSwZY/nf7APWoAXHzhfYDAJvyB/DI8Zn5h/dG9IUJxf3PBKf//ASzF5wEaQy8DH4J/gXkAhwFaP6SCKH76fSjB8T7APxZ/ff/mQCf+fQGBPw+AtUEegflCXUN9Q0gDIkMUwuHFq4FFA8sEBD9HAzNAwXygwtD+VH3jwT59LTzQ/0y9gv7Pwh++4YDjwjaBtQKuhOPEbYLLBXdCvgVHxnfCGwUKRSdAxkMCQh6BmoH4QmzCzj+tw4MBC0DtgyoAWMDOAlfDdgFgg2JEK4OWxbaEgYZvxi3DpMLcwxnBykIwA/MBLwIqQQkAtsFuQOqB2kIXAgTBEIJZwd7DtsF9woMDM0L7Af3At4FeAX6++T6Pvv3/6juYf9W+b30kAPy/c0CCAJ+AgH7TwUtA1UBKPbn+xr4G/Mc+lb5PQMK+LvuMff1BAvqg/yV+kXuT/rk9mfvSvNQ+I7mS+6Q/MzsPvvk87bsG/8o7ob4+ABq92v2J/ck/Pf3pvQo/9P0UvWp7CvyNvlq56n1J+fW4Czxaegh56DkHu6k4mztYuF18an0+uYS7tLtrPGS7Vnsm+4v7YL1Pubf8UTrSOYy7mnod+9381D0HOWT7Oj2u+Z28GbozubX9+TWxfFP+qzpSe3I8nHzl/cA+U/1Vv0L9m/1GPvk9yH7nfCS7RXyQ/jd90Hj/vqG7FDsVf4o8lLxvPA08y3wB/xk8+r3l/dj9Br4xfbIAU7uhv2K+PTujQOf8ZjyXvZ18hb5svnk71L+5PO960IBbfQg9Mr/+fCqAuf3HfgvBPT6UwP8AG76/AA8BIr3nAjZC+X9bwiP/aoObQNCBU8NW/5vCMIImv/1DB/+j/08ACsCzAS4Aa7/ivy0CrALsg1LBeUETwlCATkMNggVEhIBA/0dC9r/Ywj2Awn5UgDTByD9mAWyCVYAGP/5BwAH9Q3AC+37fw1NCwwE2AnxEWj6VBOB/ysBqx2OAU0HkgnO+jcHnP3t/GsFXPmk/+38PP3tAkb5PwGrAST3eAF+BSz9GApZAD8FiAmxBmUNCAXGBxMMcgTREp4GcPwtA4QG//2e/jsB+/4l+iP5R/9ZAL37Lfw19jP9KgMJ9Xb8DQOl+ab8jfT+/nv+ufzR+1z9SA2p9GADcgHo+gr83ABuAWYEOgcV+hL+Pv8jAzcCpfp9A9T/2flWCEkITPnV/isGkvomCFMDQ/gkCjHzo/g2CGX6iv9t/Gn58QE3++wD7wxxAmgNgv7NBoAY2AHMBOMTb/4HA5sFpfqiBML5GPoc+QL6uQC392X6Ev5S+qv+ngW//eUIr/0s/TwN7vpaD+wAEftdC9UArg6BEoUFMBC0DgEKZBKDDykMKQTvAA4KaxKl+r0GC/9g/GsJp/8TAzj/gAeb9rX+gwwZ+XIAFAZB7/ACaQAF8iQCfvrI+pn5//2kBg/+oQYBCk4KCxL/GFQSyRiAG18NwhlLFuEaxhBPBaYH1//BBqb8lv3B+xT4bfjT+AH/nPnc/WYAGvj9AlUBtPu7AbX21QXnBvr3jAy5Aw4G5A7qCgcPLA2DA6YQBAvmC+EKYwd2A9MHmQBvBPr/uf0IBQT8m/62/AT72PVCAcr3d/sb/x/5/ASq/Cj20AIrAV4CEgWCCbYD3AOIAesF6wmZDF4CmAUgByX+hgQz/SD9hPvJA5f3UvVn+3b15vjE8jP5Dv8H7CD0Q/ln90X2wQI69MT/iAE/9lcKUPgEBuT/9fkSBXYAIft//Yf/sftl/gf4b/kK/In1L/lV+mj2Lvtn9xnxZQFm8dH2gfvQ+EUB0fYe+1f8xQG1/l/+wAMeBhHzZgQE+zj2lAam9BAERf7N+KD8qPZHAoUBJQHr/vj+sPzk+jwMWwG0/zYMrP0PAM0CF/iBBm/96/7VCWIBrAC5A7wEyg5gBxwJuBVmCK8QhwqaBwAQnwzMFAgNzQeREl4FuQdXDyQK0QpBDv8E/Ag9E9oCIBT5FJQGPQsKB70HRwfHDnIIPgbpCPsJIQpDBNwMyA0HBxQTfxAMBYYMuw0wD1oTSwn7DToTOwa/AL4OqQgcDdIMsQajC3YDBwccDRQHTwT4Cfz9lgP7Dfz8wAvX/xcDjxBuBdL5EAhTA238EgUHAxP9GwGx/+D8NghbAQoDDAhSBTIBvQZx/xUFaQTUAukIPfyq/2QCpvzNBrL+qv9V/vX9GAEJ/XQGhvy8AH4KJv3G/P0CLwTEApf7XgHcA638i/LaAgH7qvwRAj73DP5OAkL29f5/ACECVwLT8CIB4vWo+rT3avtD/eT6HP1O/w4C/PgwA0UFjAT0BhsCrwB2A9wEbwBDCH/9EQKmCPv6DAAjCKQCHP7IAYIJEv50Aoj6GwJJABT7Hv8g+LkAavtJ/VIAAwC5B9QGowfnBvwQPQfVDMIMSRCnEqYI3gnFBIADKQRP/S8EHABh83X+6/46+MH7kgmf9bIISQC9Aq4JVQkqB0INWQjJC7YLTQdLCYoKuQQyCT0DzwDlBOD0R/8x/6b9M/1yBNf3rgWIBWoDdwucCK4Kkw9lCdsBaA3YCZoLWw61CaIEuhKr/rEFcAcvCJIFrv5yAf4BQf+hAT8EvgHmB2L+fgKzB50CqwWXCjcGNQF2DAH/0gTMDMMDlwozB7D8hQV6A6v6Uwta/NoCAAQI+2YAvgXZ/GIE3QdpAOkD+wHCBIz9CwovAFwIEAjX90gNVgP6/1AL4ALe/gIJN/zd/E8E3gHW/G/+WP7x8ob8nv/v+eQCzPkZ/RECpQCvCHMM1AZrCpoL5gfCEAwMmgsoDuoC6QzZCDMHXADdBtYDQgHNA4n9xwL2/PL9oQJz/MYDAASjA7cKiwX5DLYPNPtCEW8N4wPEDmIJLgr6CiAMiwlLCVMMZwIABwsKXgIqC8ALmf3bCZsNzAhSEVIA5RARD9kDChOgDzwM6wnkCmwMBQ69CpQKngrZBEwIkA8mBIMDDAiKBp0C2Qd9A3ADBQUR/5oOsAO8ADMIDgJ/CDkMjAyDEDYIlwb9Ek8NeQx7DXIIJggMBVgJ/gmqArIIPwHnBk0PS/76CtEOyAAFDi4WsQZ0CpwIiwX5DzMQdQXmB+sNYwMxDmoPMAeEBqcGlAIyBQsKDfg+Cib5Y/xiDYz9wfvvDKX+nQO5EI7+jgXQCyMANA69B0wANwPU/yv/+AgLAgEGoQGb/lb9oAfrAcv60QVq+5P81QX4+iz9Qv51/hP9cANR/yr4mQDIAS4C/gW+AWoD3wSS/ckMBQEjBIUFrP2G/MT/bvvJ/agBcv3ZBJr/rQLqBrQGmwUlCUYIigaOAfMH1QBr/6EGTAD4Acj6wf4RAlYErADcA+YDhQHNAjADBQVHBlv/RgQv+kgJYgAI/4MHt/OxAiMI8/yy/csF8PMh/+b8b/5B/1D82PJ//cD81PtCAFr4IPz1/RADJP9VBdD8dP9AB7j6hgQAB+z1Bv36/ygBaP44/g34JvkqA7H7iP5D9d/9d/+v/V/91QD6AuT29QkAB/4J8gRmAHYHhQVBCjoHOQz2A7wAXw2xBi8IQgUIBdwDZfLVDKD8x/upA672NwMRBlz5evjQA0P50/xjC5n5e/8e/5cCewWqBmMH7QuoBS/+NBPOCY8I4Q3hCbEFZgwwA1YEOwmuBRQG8wfUAW4FJP9BBoEGWgIGDCgB3AAT/WsFCAJfBKUBtPvq+5v6vAC+AcoClggn+xT8vgkzA1kENAJnB/8AZQWK/6AD+w1SAREG9ArLAaAD8QWW/BcP9wKS/kIJT/mY/koDo/w7AaIAxf3p/dIAlwLpA5YEHACJBKkDIQZiAN4FfwBPCHUBov5BAqj6Ofnh+077/vYh+8TutPPD+Hv2Yf8Z9bL1tf7380v7KP9G/aD4EgE1AYb41gPD+HcGV/80+/wDHfzX9+H2Sf06+Cz+Mfu6+5n4sPjA/FH/a/+a9zYAQwOb9ib5BQEc+iX+SfkR86v+we6977n4ivty8jXuMvop+Yvurfv683wAvfuZ9esBcPgM/kUF9QSy/ukDIAii/ZUA5//pCET7C/80/8X9YfPk+xsCUPTG+G3vDPoL9qX1NvHM8f/xLPmD9E307fcb+4wA0fftApr/ogBi/p0CSgd7/yEGzABXAqwAAf/h+ycCYf/T8KX5pvgz+TDwg/i89aL6Cfk08/QBU/20+zADiP4mABgFiP6S/kj+DATR/ksBW/tB/6wAM/kFCdIE6vfcA6X+RP8tBz38GgPSBL76FwPN/Dj+GwIY/jECGP/7/lv+Kvx+AjAD5f50/3kA7gFf+iMDxgOj/LEFuQN1+kIAVPvq+2MIMPzR/pn9FQGI+kD8WwY8+fEFIfv28LUAavcb+zUBNvVX+y4CjfCCABD9nfh4Bdv+MgEy+rf78v2c/Q77vv93//7+2fh4+uH+H/7fAOn4RPOV9TX2yvfo9cXx3/XF9SrvNPc+84n1vfMk91n5f/WG9Q3wl/eo+kH3nvop9Uf7fPVW9ZD89fnH9+f7nPlF9qP4EPkn/BH3rPl0+3QCDPn671IA/voB99r7N/tr9j345fWp+YP8WvT69+76v/nK8//1tPvW9Kf3avRl9uf7RPMx+yb5b/Xh+7j+4frs+VX+pvyC/vr/DABR9xMMggE2/cwEWfkYBtMDYf9D+Lj6cfdH99H6kfPt/HHzzPAs/QT7hfr0+pL5cvq4Afz8+v8JBNz9OflyCEEC/v6vCKX5TgY8CBP9DgrBBaL54AJIBd4FcQL8AyH/swPvCCkAvwhqA7f/WwYcBRoDEAgbAjMAqgq2A4gFEAwyAUIBw/y0AgYI6f3qAkYAywFiAN4BQwQjA80GowNbCWoGTgr7CdwINQ1NB0ALwwtpCGUJWAH2C9H/5/9wAyX69QBX+/L5YgUn+MH/Zwpy/dAHHv8kCoYIhAIUC0wIGgtrBewH/Qt4CUf/7AQxBgUBjgJXAvz8KwJB+wX6agtn9276TgKi/s/9qv8FBUv7TwDR+48AbgJQA9T/wgDVCbz9tQXoBU38BAeoAXr37QaJ+Y38+P42+c38E/kE+2/5kgCF/pb5XgYY/isC9QkmALgJSQhuAp0HIgkbAkIJ5gPr/sUBqgKD/EMDkgDk+wsBy/Y3Ay77uv8/AdDzUQLr+i34lgR39+z4WAHY8pr/Hv8Y/+z5ywFi+hT/PwmJ+U4GTQOZACIBBgBx/4n90wf4/vj+5/8h/9/9OPr5/E7/wfdh+2D40fr++mX68Pu48g4CufSy+cn47PQf+YL6lvyh96v6G/cz/Uf49vxvBH73a/609zn9/PwH+LH/5fVD9Bj6lPdS9Yf3/Ply8dz1sfrF9u33JPwH+AT8Nf7o9mgFH/X0+7r7CPvr+j34ufkn+Of3hf5i+Sr4dvxu89r7xf5H+2T30PhV9jf3zfOK+Cj6XPW687D09/sd+An9Z/fU+y7/hPut+z38Q/3d//37kQHM+cH35Po4+hT75fmx/3Tza/bh+gUBavwj/fsFcf+I/q0GXAR5ALsJewGBBmEGYQJwA/X+VwKeBkAHMgHoAOQBe/6hAasBDQfI/j7/VP8a/B4CrQLW/C8B0f7+/iL6q/4lATz9LP2KA88Av/3gB7X6wgS+Be8E/wj0/1X+0QGK/yQCXgWe/6gBDvvC/b0CcgCIATADiP4GAMECCwHk++wAdf4P9jEC4f6x9/b4bvfq+9f7O/vt/838xPv++kv/8f6U/9X9I/1aAun9vfzp/JEBWPqX/3IBU/1P/uL5sv7H/8UE9P+6+/wAngGo+qL+MAdGACv/iQBh/9MHOQCFAIMDrwRlAbsBuAGM/egBZ/yFAHQGHwAPAQH//AOICSH/iwEiBSQG+P1oAc4BQANB/78AyAHgAskE9/t5BO37LgYYBQ/+ugID+V4CGwGrAZIEL/5M/Xn9HAAdAz7/GwKC/Uf/0QFp/S8EBgRY/iMEP/4wAxUJ4/zSBO4FxQB+/yUJywFiBHv/PwB4CR/+C//gA4sBjP3U/4L+tfZHArv6gf8Z+YL9Tfzm/Jn9//mtBsL9/wgJAEP92wnCCNwEJAsaBzwAOQy9Ap8Iigex+jEGwPy1AFj+0v3W/Nr3wPwW+a4Bwf9U+4j+S/pLAgsB8AKhAj8EGQAAA8AD9QAEAhECWQAGAPUAnfdCAewAEv4HBzD89Pv9Amv+Iv7x/z8Bjv/5+N76cvlVAQn9tfmm/Rn9Pfgk/OH/ufgI/wj2AwAG/Rn13v54/o/57fx2+Rz15PtD+Sj6rPV19hr0i/Zh9+/1NfaB8tj1WfUU8yv2N/xy9ib5qfm9+Af4JvmfAIr/Ufvp+O7+6f3r+oD8D/7U+9/5xfU39HD8W/Ks7Zn1ge736z/5Bunv8BjyN+fR98jtGvSY9krv+Pbs9S34ePoF/ib9T/oo/zr4SAGMANX5Qf+I+pv+X/lV9g/14Pip+aHus/jf7QHvvfPX8zf7Hu7/8MDwqO6V9h/2R/cl9vz0E/UT8RUBa/5JAKr7QPwVBaj+EAT4BF4CMAMoBW77WQDcAAEGRgBxAswA1vyCALgBYwOZAN0CR/zZBFkAZwJfBGADbgLo/oYDmQN5AAP9yfyHAgT77wCO//b4lQBq+0X+MgWe++v62gJWAOr7BwO+/nH/JAb/AN4BAQI7AfUE7gWfBB8EYQYs/h8A/QIpBAAArQJf+R/+qfml/jQCcvkD/fn4X/2F/m731f0uAi341/ui/v/5GP6O9vj6zf9A+Dv/U/Ro+lYAuvdD+SYAQfeK93f7xvj1/XLxqvcD+c72Ffaj+F/66vfR/+T7owMpBBcClQCR/w8JUgHh/g0DnwAZAFj6SvxnArkAVvWVBdwAJ/zO/k38awUJANsB3v7wA6f/sPzkAr4FIgFDA9QC5AIcBQwJzxA6A4kMeg93BqYQ+QxcDFQPDgoSCcgR4Qn9BoQOuw24CQ4KCREkChsGSRCOCnMMOQx5DAMMbgrhDekIxAb5C5kIBApFCTj+QgVEBg8A1QQzBH7/kv5//akA0gDK+837fwA7++j5IwCr+nb9Bf7OAeH+dv2G/I383Pg5AJkApPMX+DLxEfMM9f/1huwx8/zwQ+hV8q3v7PEX6zH30u3z7xr4de6F8tX1uvMA9a723PFK+NjuR/u78uHua/4m7dPwlfYt9DfvM/lo8nTv8fZN9Hr08/Sp9VP0mf3e8rX6+vsK+OT6T/4kAp7+Hvue/sQGovqKArkEnP05BP8ExgfRAZ7/EQYiBRgGWwlYCYoGrgHDB+IQohEFDj8RDRMwEFwZ0BvOFgMcVBrHGoUVwhQGHPQWZxJEFrESQA+6DsAHhw9PDUcGgg3CBJEB7AR6Bs0DEwQSBVD8KQDR+5f3egIC9rn4jfhc9cj1uPGL8if0+PHQ77Xxi+4c7cDr3e857Zvy3O276mXqke/I7kLtL/JR6uvuBunF7YvuBOtg8DztSeiO6t7qbO2t65LpHOnP6LDr5eV86ZTr5elu6pvubO1973juUfNY9jP1yf3S+Q77SQBkAsT/MQZ+Ce/91wbnBlkAlgh+CvIAHQcrBvQC9waVBX0LqgvGC9YLeg8HExUSYRqXF3sezyHnHpkg8iUYKpUq9ytkK2MswyiRK+ksSS2LJush3yQIIoAfdxtBGkQbGgtMFJ0TsAveDbwA7ASNA+cCy/rV/lD0uu8Y+nnxRPON8MTuv+WN80Lu9+6Y7m/pWPL16QPwyfB071nthuyY6Ubxge4t5Ars8+ti5bnkPuuP5fvlfejb4qfnEuXu4mTn5+K24M/keN4C3rbceeCt2wndt96X1lTe89892w7nhuwK35zpcvIG8Qf4dv3u/pb9cAMFAZYHyQsIBdkHiQjOCaQGXQf2B/gInAgaB3wQaQyyCfwUaRRAF7shoiHeHlgmBywHK5M02jN8MRU2fjOwNDg24TaBLssucjLAJ/cqDyaAHyYg6ho5GEAX/hb4CEIFAQreBZf///3R+43zTvOd7wbxifBn6/TqdOfa79Xpd+e78tjpoPDk8+LtRfJF8sfzufR49tfv8PPs8a3r//CR71XutugY6irnEumf6RDkvub+4TXiAuVw3x/h2t6X2ofaKNqZ2KPX/dpb2jfTP91333/dZ+s657TmaO6T8APw0frI+qz1hP+G9Nj+zQJD/JADwgSjA64Fdw/rDRUSuxk0G2kghij2LPAsPDlbNmc4JUYuQ4g+IUS3REo8PEJCQtM4ZTqRN4sqjSxZKRQjdSLVHcwUCw56D/UIRAYXB1v/cftG/fX5xPPk+3z5Ifd9/NP0PPlo/pn06PVSARH7Mfc0+/L59/f/9aX5ePpK+OX1yvOV9vDzDvMq9IL2SvM/7UHv7e9n6wPtY+jq56fnydzS3QbkU9w91yncgNsf1dPXIdtN1x/hNeFH35/peucw6Hvy7/WV8bb8PfwD9GH/APiPAIIASPrOBeX1+ADsCMP89woUDg4O5xIgGHAftCOsLeUx7ThAPE4/3EWgSNhKqErxS4JKD0ZaQAFAQj7bMg8xgilJIM8dfxVhEqgNpQRkAmb9Nvl+90P0xvAL8yrwOOrA8/Hqc+w/8RrrZvE+7v/wQOyp8NTzTuuC9RP0yu8E87D0pfKn87H2U/RO9oH3/vZS9V34c/Tg8GL56fCJ7Vfv7+TC5efi+OE+2nvaLN2Wz1nYstgb0lva7uGx3n7mIvLm38n5egIO79UNdgiQ/JMD8QWyBOUEiAkLAW4Cd/9A/PwH0QYmCIURohCNF9UdMCTMLJczqD5tRClC8kXmTX9K6kuUUxZJNERzQeoz8jWRK0kk0SL0Ft4NJQVh/7387vpB8//xvPAL6hTnF+dh74XqUOwQ8G7qae0T7aDw7/Wp9ebwRfYF9izxT/Xk8mr3OPbs8RnxJ/Ow8G3s2PWb8ljuN/Q77rztCvCo7vLtbe8L6xvikuH33uLdktVW1PbTl8pPzNnM/cqr0cDTy9V84WrjNukZ9aD4uQDXBhsK9A52DAYQ4Q3tCh0PiA2aB7sJMAcGBHQO1ArJDLIZUhkkG5EmgzDqL5A4/EVIQk9GMUyJRYVKDUgdQN1AWzrcLNslCiTNE68QLAxw/Gv6TvPz7DnpX+US6fLlu+J45TPoFOes6S/yGPLb8i/xD/Y792v24ftl+tv6Rfqw+Fv6EPxk+6v6wPjy+WD4MPRF9kz1Ffa378DwA/Sc6WXpB+sM6ULl7d+Q4CjaFdmW2B3YL9l70czYUdaE1lXh0t2k50ztP+1v+Rz+cv7fCDUNLAl2EHoTlA47DgkUqA2WD6MTFhBHF3cbuRglHo8lAyQfLaI19DZfOjNBgEB9RMxJ/EVXRH9F2DqtNJs2UCQdHPEa8Ql4/iX6D/Et5G/l196R2rDfSNUi2aDjadz64+jynO2t7+L5EPiP/WIAiAFKBxsCAwADBJ4Bzf9xAr4BmwHm/Lb8IP06+Mz52fQB95Lxa+Zb7u/l0uHT5KDbJd6k2j/Vntph2kPUntXH2vbX+Nkk30jiaeS85GLt0vF18l77of8AA8UF8AteDiQLKBLmE58M5xJZFO4J4w92EP4NhxeOFg4SuCbCHVEaijdXL78ppj1gRZkwUz0MToUyZDtKRPQmqyoyJswMahOzB07ztvSO5kTfgNvD4EXdqtIX3zveEOBJ7cXxTPUh+2P94QEuChgKNQnlDZALvQM/BVAHa/rx/owAevOy/Yn0ae3Q/Fvyqu9m+GjuUeYk6zfnEuEM5bXlzdsc1SLl69Wp0AHrTdt910Ll19po3aHmvOTQ7D/2T+3q77769vhM/SkI6QiNB/kL9gdXDnURfwxLEpUVBhB0DgkQdw5kEncXbBywHyUe2CFBJ/UxgjUlNn5DeT2COVdA0kI5OZYw7DklKmQTSBEDALL1ne/e5iviedTrxRjOU9h10efeDO2e6uHmOf0zA5AHsBuSEQYYhBpDDOoWuRh2DKwMLAAk+xv7fvIS9sDzeewk7/fzMu5682Xy/+l/8ejyheWn54fjrddM4SPgWdzm297VWNZA1+ratOKh4gHjhOZf6b3vHPpyAewHOgs5CDALeA2mD2YUIxTQC+kLfwzrAfEKZwvbBWIF4APJC24GywltEyMUQCCUItwohC9gLK476z4BQFhH9kAgPXI2QzQMLZYc3h7HCpfrMe/83GPQEdaOxW/I6sZ3vuHFqNn35rDjZ/9kDg77IwwdIMcb6Sh0Kw4fOx6SDJ4KJw8UAuT7QvK3623jHOU56Znoheon65XxKO7a7y30J/NA8KnxFO/i4bzgOt8u3sPbYtlc3AjaAdOQ29vhCuOB623vOPoY+//9bwxVFYcXbhqAIE0XlBJZGNgVAQ/hBj8EBf6R91v7+vuv+eT2WvwlAfEFVA8dF3ofkyiGMS00njtwQMJB8Ud5RVBFgj6KL2cr5x8XD1EG9vya63LVQMtaxzO7E8cuxs3GQ9Bdty3cgfKK68MTCRnKE+IUKxoCJpEm2C7/KbIVpAq9+1L1FvX98+3rgN+12WzZT93d6xnpxOsL953n1fGZ/RbxVO9v7WLtq+pe4mffdNrM2LrTat/b5pbcAt2J6ITrefEq/JsFbg6pCDgVACB7FXgecyAxF6sVpQ3FCBQKqAER+/X+wfYk71T/rwCe9uQFGQiODQobECinKyErQDhtOBVCJEz5SddHcER8OeU2yzKaH7wY9Q1x9xzpdNrOyb/AKMEKumLAR8aAv2rPOtc/3TrzEQaxFvcnwCPuIkonXyWYKpQz1y8CFUcDIvZc7R/tkerT6Lngb9Fo0c3fNON46hL28/Sk7jDwjPEn86r3jfSk7vbjv91R3lnUnNw+3qPXAuUt4/3fKOp76gn1AwjkCu0PJBM4FasRmhtlIiEX5xJcCPj+h/dL9hT3uPKX73Lp4+hl6lTvffxYCRcTXhL9Gz4n/SsrOyVHREyCRitLRFRUR2RLmU20L2IxyClXD9MHSvjD5GnUwcY/vEK4WbNCuFHCMcrSzAbQZuSu+h0DiBlbNm0nlCd/MQEvbi9OK3YsxB6PCKHyqOYa4/7VdNp13U7Sms+f2Dji+ue09zv+bABPBfsFBwN7+mL54vn88J7qiOEa18PPes+Z2LPX4dYv2WTiNeaE6+r70gQADFoX4COzH9kg+CH6G9ofRBJHBzcCJ/NC8crvp+s+5tfiyeQR5yvybfwVAeYHtxKqH2AoMDTTPLs+lUIdROJJIk5NTJ5LxUaoNucneSQNE7IJXAB15SfPY7u5s5m3Tr1ytHPDBcFluR3PmucUB5IMwiXFNlIh3DBsQnI+KkTTOFsmshH8/JP4+ubW2z/QgcLdyn3H8MrV0S7Wiez78hECeBGDA2MICw5ECnsOmwX4+ezs29152FjWI9j2003P6dcO0ibYReYD8YL5Pv/WEO0PCRVUIygi/h2xGgQTHQufBOT6/fNl7vHmDuaP5SnktuTY6WL18v33BocTPxm/ICo0hjnyPXhHvUgNRIlFXUybRtU+rUBXNNkkFR57EWsFC/sl3hnUd8ohscfCOMUUxkC3A8NO0ubTLPkqC68YsxztH1c3mTkXRJ9KkzzBMsYcMhUJDEjyEOxY2vrKUMeXwrXI38jGy5/dr+SP8Vj6rgHfDE0H7Q4SGRAM7AM/+cHue+op3e7d0N9uzdXMz9j12aLdiehi8dL1QvqSBPQSFxfhGgUeWR0BE5QKeQgX/C/5kvF958vi4dZP2brfouEE70f4mPpVBQEOkiEDKVYwU0HzQGw5skHaRxI+wj51QnY5NC/CKbAnWSBoGeMT0QEk6xTbQ9Q83MzQqMmUzo+45bhj0KveXeMi+uUJJwaNG1sqFDPZPbZAcT9FPhUupx/BFn8Ezfdt507aQNQbwjLBLMyJyI7OQ+RW7PvtbwChCt8QrxigF8sZcw+pA+IAp/sj6G/d1dl9z8zMis6d02nU/NTK2uLpKu9V9rQKxg8/EW0f4iE9G6MYcBdfEY0HjP3k877qj+HP3FPc8uFy4YTjavD18YX6OwoNGMEiWS2rLjMwtTakN4lBKUZPRt4+VjUlNlIxoTJfNTw1UR4J9Rf3Ueab0Vzpl94AyKyzhK23toXAY9cr4oPwwvUPAe4eyCrVMoNF/01fRutGZT6oKhEXKgsvATntf92+zY+83rHwsom/pcizz+zclOfM8N7+ChMOHxQf3yGcIU0ThAq9Bk/6bfC642fWJtTsywbINszFzb7RStuz6AHrmfUiAQwIdBYpHNUdnB3lFTgRwgjKArX9DvO/6aDjOt/J3IjhC+L86KPwW/YU/zAMfxmjHGUmfi6dL08yUTMOOzpESzaCMkU6YimLKulFOUHwK+kcoQrN7+DvIf867GzY0sRSsIOzaLFxwsLV2M1+3ifvMvpoEc8kTThGSXhPNEyFTh5HkzSNK8shmw0t9MHiMdJfvEqy9rPKti+8YsUrzdPToeaC9i8JFx94Imcf4B8hH+MX5RWhEpUBo/TP6CXa4tS+zVfLM9Qd02fSDNUj2ADkIe/oAHgR+BFcERURuA3OEX8QcxDpCzX+jvfq67fm1+fD6DjufexJ7QPw5POe+/8EARbeHjEXvR9mJHwcqyaHM6E3LTB9KLwpBSpLMmxGE0W/OZMg+QsgDET/tf4u83fblsu8vHC37bJjuxDH2Mkb2iTvBPi3Chofdy8fQnZRv1J0VL5P+zr1LTIhXhLi/a/pa9WAvxK0XK90soK83L99w8fSZN9o8kMEuRSsINEe0SIOIzYgTBhRElAPcfv28MzoBdkj1OHRw8t1zRzQ6s4i2X/lK+rU9+b81AG5CBYMzg6/DJ0LAQak/6b8Jvns8CrwUOz/6ZHqKeUX61LyH/Z2/A0DyQzGDL0TuRzaH6IhCyJkK04mGyIuIuMb8C+aN585eUWGNDosuyaLGm0fgBiH+xXq0+Psx5a/2spnwgi5bcMozs7Rreel/hgOzyDkLoY90UOlSohSAkuNRAc0bR/JEDf3QeYB273LPsKzt86wdbAKuoHBENjf7ODwEfttCxIVkRo3J6AwYyTbHpAcOQzvAJr8kfJJ5E3fd9e+0cDPY8zo0QvaReE97Hvyf/X/+TP8TwCGB80KUwcu//UAb/2G8ID0ofOL7sLxfu6k6jnkCOrM9P/9sgjzB8j6tAISGVAYxyLVOrAXhw9aG3sesCgJMYI6OjQFLno0ETPDMFQzXSjMHIkUEwCd65bg3dIbySfKUsi4wevFR8ZA1PrmZ/xVFdAj7S+QQENFtUq8VlZRu0Y1Ov4miw6g+Lfrg+CbzQ7CNcHQskixlMJNy9HSFuno+uj60AdPFUwZkh0bJucm2B1wFy8RAQY6/Hz1bO1351TjJtzd23bcn9h43vjlMefp7GbwZfKN8Nz1D/pw/Jf7tfWC8anwr/G28Jn1oPQV8grw5fFH8yXyHfT4+mMLAAeO8rQGlgg3Am0jxCfGHEAQUwfpEFUZVSEnNFQzZy9cLfY06TnDPG47dzcIJgoXnQN084rnV9eQzz3H1L1xuTS+pcQX12bsqv+jE+EmkjGMRbRQpFjAXFlWhUaON9cimxWAAxLy3eNfzXm/dbjjti7CV8azy+LYo9+x7lkMkxSnGzQjmhvXH14iECQLIsIULAhT+Hjyb+2V6SDomOJf3bbYhNb01oPcVuQb6gjqs+zS6crrd/e5+Ij2Wf2p9DjuqfTF9Sr0LveE8yP0PPWI8vP0HPrI7tnsHPms9QTzO//09lb9Xw0LFq0X7hl0FlcKMA+MIa8xHjfhPyBAWDZyLVs6PEL6LwcrfiJf+pvq8eLu0azMl8YiwU2/M7+dx9fWF/BWBBYYLTCYPtI+IEldWVxWqVGPTp0zKRljC6b9E+0+3lXRPcMvvFi1+r4H0xnYguUX+Lb8EwQ2FNkgECxoKscfPx3aEjUJ/hFLCgMAzfON41/dRuAm4Drj4e7d42jdkebf6FXmdO+39/n0be/k8kb1f+266wfwpe1V6r3vNvFY6ofzO/d59ff7n/lV+vD8LPn49eDz9Pbp9GT7vv+R91nxY/1SDe4NbBDYDRAMHAWkErA0mjzkO4ZFhEPFNTM1Aj4xN3whWBoZBBnl8t2Qx52+8MOFvau5usodz1ncAu4OCrglLTghR/JWIlOuV0dUZlVKSOAwaSAmBPbsnd/hzqTGM8TXvXm7qcPGz7bcs+w/BdEJigrjH4UmNye4LlcrJiEbFpYMpwYvABn5DPHN53LhNNqb2XDc+OEM7Trr4e597xXuC/IK+Gf8wfvd93j2rfSR6tPnbefH4qTjUeYl5oDnaeQ26TTrvO1t9P/5fP1Y+pfzBPNy9U/6AACUArsBQgBk/1YD6gYjEMsZiw7yCPAbER89MJRPR1hJQS4zITsLLqYkxCtaE1/xTdsA0D/AV7bktXu5v7wTyA7a4OM0+5kURSolRnFcsmIWYr5jtVpRTHQ/EDBQGCn9i+Zf0A/EQb73uU6+PsEnxr/UcOjm+OwHuhv7JqsqLzK7OtI1mSzRJjYhOg97/pr7E+2d33ffTN3o1WXVEt103+jloPB+8sT3Qv4iAYIBlAL8/Wn9qfnI9oXy9Ooz6GTj3t0f3aHeFuEe4uXhkOSS5Wbs4PQB+1v/nfxB/yMDpf6F/ggKfQtWCFEGVwpLAjf83QsQFPQONAY6C8UVVxe8MWhPUEnZPRA1ACTaHx4e5xLGB8XxONL6vrS5VbXeuTfGItEt26fnY/g/EWAgFzygWQxjMWQ3YcRUpEOmPLkw2hs5AJTqlNYZwDq2ZbkuvT7Ccc4j2GveSvN6DwcjjjLdN4c3njapNeozRy+XH/kPQf8q7FXhVNu+2nDbVNdx2s7ZcdoU487uNvVF+isC1gejA+oCsQJ2/fD8Bfqt/AXyheVI5uPfSN7x4lvi5t/H2sLZKN6L4uLlcum19U72/vax/04BjgIQB5IIfAwADNQKhQ0KB4gBHgZnB/wHVghrCuAPOg/ZFD43nEWeP2tLgkIyIdgNCR0KHMj+O+4/0PC2OavLsIXE4MomzBXeNO9QA/cWKS0GSW9WeVoGYghgIk/lPt00qieODq/5uOo90/3CUr1cwNbDA8ht17zke+71/sMP4SKcMTk58TsfOqw1SS2VIXQbMg04+ijyaOZM3ZrT3dJJ2KDbOOKR6trvF+/58K/5gv7yBF4KbAjEBqMDjv9Z/cX5jfP/7Sfnk99+2sLUJdXW12rbAduP2R/ZXN0G6Xr0fv4wB1oHIQadB10LXQ81EdQOOA64BRT/6ABV+i/5e/6o+rkDaQByAKATOx6rKr1ERlEwSd896zYeI1sNahNvCFboBswjt2edXp3BtTHCycsv3RTn9P8rHrE3dE/8XUBlv2KuWxVXlUaRL+ghIQ4i8kXed9IIxbC+gL+CxJrOHdeg4xj2JgTfELQi7DCfNf079DuYMmUqHCUcGTEOMgUF8v7mZOOC3c7aW9644QDciOFQ8Nzw3vJP/Xf/7Pkw/AgBHwH4ADcCeQCG/Xv6xvST8JDsg+Sk3hDcHtof1UzU3df52AXayN274jzpKvSCAZMLmgtLCsYQ7AwKC3URSBKaCwwJ1QBp+Tzxb/Gm9F34GQC4AcT/KgOIEb8lZ0DETzZaVUvFNqkgjxESHRoLQ/hI5lC7w5IflP+n+rqsyIXZp+PC8d4N5y/UTy9fMGUUbeFkyVXiSaU2ZyfMEL/1C+Z/0Sa/OL1cwE7FqsqG2IfnN/Q1AZcTZSV3L5s2RkHKO+0zzSvJILMc4hWFBbn5r+1I3prbIuFx4pPgVeEt5EXlcOzG+EL+7v4lAfQBjP1i/k8EdgRuBT0Dm/4q/GX2be8B64DoNN8o2ibYvtEAzBvO8NPC2K3fjuLT5DLujvowB20T3hmcGMIV6BH0DkoPwAsqB9r/cvX/6D3j4eLm5Kvq5O8D9T8AKP7hDbEuPTx7R3JfI1ncPYUusyQJFMkDvP1v5d/AOp5OlLqZmKh2w0fSiN7T8OkDSCaCSvRbl2RUbElmrFZhS/NAKCoFEUIAsOzR0kvFyMCEvjnElc3O2SboK/OE/7IRdSGRKnI1WT1+Nz4zlS4KJBId4hlZEHYDSvhW7UHmmOYc6dXp2ejK5/nkderW9FP54f7sBFYDO/7/AFADwwMtB6cKKAk8BDcCj/0I9ljy/+x85Xff5tfpz3nMU8vVzdjRANTK15LdxeUu83gBCQzDE2UZwRofGVQXZRXdC3gF+/5F9tDr/OT62mTaZ9813rjlZPMp+Qj/URLEIig3KEuVXixrvFYUO2Ytuxm5B/f/l/M/0WSlB5HdjauYQ6/cx1zZcuXb9twYijc8UoBlBmr+Z4tfAFEDRa4uPhpeBgfvRd1W0JjF673sw8zMONUW5I73vADpDJMcASfqL5o3nThLMn8pMiIeGncXkhFEBvj9wvGo6ontkPCB8r/xw/Ay8gf0jvpkAosFewEZALUBBf6u//MDBQHYAUADUAO0At4Bu/5z/LP46PKO7m/lRd19157WJtQq027SJM/zzyXV4N9e7kD8cAftDm8RaBUtF0UZdxuPFJINpQR+9wfsBOdV4Rnc0tyd1x/RVNd/5anxDfzpB4wZACB+Nvxh1m1GYqhb+0YeJ+4ZWxKh/+Hmf8RapliY5Y9TljutRMLBze7hiQDCGS00A01PYkxq7WhpZrRXn0JqLCcXOQC47nDggsx3whq/i71Uxm3bYe6v/aYL5hS/HTQnRjG8OX86kS+PIXsVng0kCsEJ8AeU/+X1XvJM9dL5Yf/cBLwECgOWAxcDvABu/2f7gPj09+vyEvL48mr0yPX6+3IFigcmCH4J9Ab4BWgJvgmlBBD8J/C/4d7V0tAYzjvKu8keyhfLWs8j3IjqavitBt0TsxgIGnMcLxk4Er0TXBE7AhL2Y+xv2b/Ma9UY3sLVVdXd29LYvtXk77YTbB2/KSJGTEqiSrZdyGN7TwM5ryUZCLDwr+Ci0FG6h6o+oWSdZaT/t9XNu+ZiBDAcbisoP0tTqV37Z69mJlVMPcQn2hJ5AKjuxd1XzgDHF8cjzJbTGdyx6jT/bBUyJuIx8DMHLzwlwyRqJ00foxdJDID4Lut27NjyNvl5ACYI1QkhDlQTrRdBG8sdKRzkFiwNlQDy8ZXpxuQA4Iffp9453aHio+wX95MDWw16E9wYsR5JIPkc3hliEWcGN/xe8gXm3Ng7zQTC97rDu7m/L8im177i5+7g/CMIXxHIGeofMiF8Hd0XWg9lAS30jO3D557e1tcy0VPQw8trzrvevupx7//wB/z0AlsNDDHDUdJWc1UaUSs/OzI3MDktHBlq/EffdsjXtT2ygK84rGG2jcPJzz7m5f2iDSUiKkBWVW1dmFs1UwlBvS8JJdYYCAVQ8K7ZmchKw+vJGdQQ4HPs2/Jv/iESgyT0Lls64jnuLtkk7xweEhAInwCJ+eDw2ueO5gPowu1f+vgJNxOJHHkgfyHNJ0IqBCesIW0XrQeG+KjtHubU3qbYEtVF1frWfN2p6Jvy9Ps/CN4ScRpZIeMjLyFoHq8ZKxJrCXv+NvBo4X3TlcnBwf69yMDxxfLMcNcg5LnwC/9GDPMTixqoHfId4xuKFzcP/gVF+vrrc+Tj3xLVhs/h0gDPP8g81GTn/uKA59j+gf+9BlsmbUA9Se9V618MUgpARTZtJKoXLAyO8kzYEsEkqQWcf6NosDq7pcjM3Dvqv/2qG6I9r1K9XYRgPltETAo8Ry/vHIIELu+s4EzQVsPPwNzE2cx43dvyKAVmFAsiLi+cObZBzEGEN4cnvxRaAtz4wPPU6gTjCt/+3grkGvAcAMkMQRaHH1sqYDAFMkIyQi3GIF8ViQib+vXtJeHe2lzYotQL1qPbfd/r5iTzmv+9Co0TAxiPGdwYrxhJGHEW5xLJCKv+fvZG7X7m4uHS3G/ZwtiT2LXZQt3M4Nzk7+x08y739Ptb/zkAiv90ArEF4AKPADv/bP3M+bz5Nvlm8T3sku3j66fnNeFN22LVTtb04h7ufvs3BmoGwhXOLmc/IFEKWX5PCjjEJ5cf8hAW/cjuq9nAvzmvfaqfq261DMgK33jyggDyEIQnIT8GTmla7l/PUns+gi0xGmADkvG349zUBcmBxarG/MzD24XtnwCHFnon1S2WNEo4bTTcMZ0vVCN3EggB/e9F4gjeleGB5oPsxfHY9uX+ywl3FyYkrylSKSEnkSL4HQgaEhVDDGkAv/X66w3kQuHC4b/kZeq078rz+PkU/Az+fQOWCO0KZgz6ChIFDwBmAGoC/AN+BjIFWQAk+872T/XQ86/xKu8E6+Pkz+AA4JDf4uEJ5YTn++mJ7BXyu/YY+/r/sgAAACsCuQSMCP0KwgwoCqEB2fhg8LPoEeOe3vbc8Nu41drS8Nt75l/xhw9iKl81ZD+dSNhKc0lKSFxCGS2CEeL9rOjk1kPLRb0Ns2SxX7jexS3b4/BIAR8RWSTWNDtDnE7IUwhPuUFOM40jExD+/iXyXOWt12/RA9Bz0LrXa+a/9eIEKBJ2HHEiAyUoKlkt+yokJ7cenhKYBY77K/ZC8ozxxvBe73Tv0vEO9/f/pgiLEW4WExgvGc0XhxZPGQQXlg99C8kETPmb8vjyv/GN8E7zbfQl8o3wyPJ6+G38wf+ZAysCWP6//aUAzQISBZkIeQg1BWECnABGAOr/a/4E/G737PAF6h/lFeJp4NbfD+FB4pfjXeSY5tDrLfB49oP8mv+4AZIF0weiCU8MGA3ICbMDwv35+A/xtOoc5VjeDtJoyVjNnNCo2cvysQpBFhIm1zfDRNNN5lEhUC9C9C5dH/sNRvke52XVUMOZt9yzALfawknUluQC9jIJQhmkJ+Y4dEekS1NJAUT1NRojWRTwB7r7pO+F5Wzc89Os0BjVs+A+7yj+nwzKFpQbSR0yIUwllCYpJZkgFBe6CmwA8vmV9gvzVvDE7t/tE+1z8Gz5UgEECg0UrxhCGQYZQBfGFHQS4hAFDm8I3wBT+Rj2B/Q/9U34Z/ev9Tv2C/d2+cX+nQIcBG0DvgGU/yr8P/pi+tX6Mfuh+yf8iPoc+c/50fse/yYAIgG/AM38w/hu9mD0+PGw78bspujJ5ALiH+Gk4i/lXehh60XuDPHT9NX67f9UAhMIKQyjC0cPOg+bCZIAwPhX79PkI9z61p7RhcnHyt7ZDezY+mYUmieTLFc3l0QGSfJJMkpOP0os8hT1/qTrWNqkzsTG1MENv1fCPcvF2S3sPwAlFqMo9DNaPDxC3j+yOoY0dSmYGSgKdvkO68ffgtlv2bncaOG46fb0vv59C0kYHCFqJ6sqbShNJHofjBiLEYUJ3f8b9mjuEukx5//oX+148iD4jP0gA8kIpg9IFpgZ0xuEGwkYrBQcERwMEAhDA6L6x/Oa76DsnOyz8JD0R/jU+5v++wF2BOYHrgrRCmUJTgZCAZr8+vfw9Przp/P68+rzkPSQ9Gz1c/hq/Lf/jgKpBJMDOwEAADb9DPpS9qPwe+q85I3fc9xF3YTfaOJO5tDsUvJD+EgBLgq0Ds4R5hTVEUwMdAbL/vj1C+sl3b/UlswfxAfLm9ax3jLtXfwpBJMQcSObMqY85ENjRY08MzAqKJYc2Qyh/+rzPuJ71VfTSNHu0fTabOQd7E73pgNRDk8Z0CP3KisuGS08KQ4jOhudEwgOAwjSAFb5+vOU71PsSO4u8672evsiAZgFiAlzD9IUGRiPGWIZ2haLEhEOvgkEBqQCaQD4/ob8Vfq093v2bvYX9zv6/PwS/gkAjARJCMwM0BPwF+YXjxjnFjURogyNB2kAXPlq9M3v/uo/6fvpmutR7xb1N/wfBO0KKBELFhQXXBSvEAwMfAQM/kn5R/MW7WnoT+XT5PfmsOse86X5/v5JBHwIQQoUCzcL6QhmA6D8MPTX6t3jbt7A2zzcBN8K5Avrk/Sq+2gBhgtHEwoT6hOXEv0Gsfcy7v/o4tyK1yfb39TIza/UnNz14frvpwJ/DO0S8hyYImYkoCfMKYAoOyL7GVsSWwbN+2v2tfLz75TvzPBj8InwqfRM+Yz9WQQ4CqkMDg48EH8Qmg+vEIgR5g9zD24OvQr5B9EFvAS8BHIFXgYnBgwEDQPtAvsB9wIFBUMEFwPmAxYEeQSkBnYIEgmoCdUJVAq3CnQK1wr0CkYIzwQtA7T/B/xT/Kr8uvui/Z7/1QBaAxoHDgqQC1oLpwo5CAwFYwNrAe3/eP4K/JH77ftY+l77O/5L/sj+iwFLAVv/PADk/wj+5f7R/rn96f0z/bH7Nfrm+Ar4CPaB9xz6M/le+27/Tv9+/hEClwJ2/K/5wvkT9JHvmfFq8JDrxuyi8fjy/vZY/rf/Ev4+/zv/IP0Q/Ej+HP3R9iX2/PX9743w1PNd8Ovtr+2Y6uPohend66vuQ/Co8qf3J/wSAYkI7g03D78QMhH6DoQOVQ0eCgIJjgbeAVYAcv4e+9L5P/qe+wD95f74AfMD/ARWCI0LUQ7YEXAThxPHEjwQIg06C/wHgwPOAVkAj/3C/fH+JP+/ACADSwU8CBQL1Qz4DUgNwwtNC7gJMwe0BoEGSgMSATYAUv45/eT/HAQ7Bj8JZQ1ADyINyA28EKoOLgoeCr0GOf1++2f7YvYe99f7ovoh+2H/zAD0AjMIDQsgDGgNLAwTCIMDZgAs/XD4K/bj9Nbwce4f8QTzf/X5/HQCWAXkCmEOBA9UD4QOnAyWCPQCMP249nbwVuzI6YjmEubq5yLq9+9r9o38nQOWB2gJqQxlDS8NJQ2FCdkDwv1U9wHz2u8h73nwovF78oL2mfnV+qX+RQHFAMgAV/8O+wT3MvI17l3stekP6XHqSuvc7TbxYPS1+aT/nQNNBy0L6QshCnwIpAbMBGYDQgGK/5b95PpP+jz9gf/FAQEGkAceBuEFrQYXB0kI5QhqB6gFVAK3/2v/XACqAtUFbQcpCPEJkQoJDH4OCw5BDgcPoAvBCZYIxALl/uz9Dfzn+8n8hvzN/GL+SwELBvQKYQ7/ECgSGxL3EogRdw6yDKoHNQGU/yP91fn/+U/5SvgV+mD85f4RAigFrAhQCxIN7Q5KD6EOLA0WDOsJ2AUOAk/+APkA9T7zwvFD8NPwaPI+8wT3jvsf/sADbwnhCeYL3Q5BDrYMNAp+BkwA1foE+OP0cvFt77Tu5+6N77Xyfvcr+8X9LwEjBJsFagYNB+QGXARlAXX+GPph9wz17vL08rTzqfS+9nT3qvd2+VT7uf1O/7v+zftE90r0ufSm9B/12Pbc9Urz6fR9+Hj6rftq/B77Fvns+PX5ePoP+jn5EPiz+Oj6l/sw/Oz9ef2Z/ej+3v47/6T/a/4cAL4BQgDFADYARv3j/JL+Hv95AMgArABUAikEggXhCWMLJAodC7IJ8AZKB3EGLASfBIIEZwLwArMDIAMDBOMDYQJ2A88EyAXHBoQG3gV9B0sJNAu/DH8NRAq6BokEJwIRAqoCiwHX/+n9APyb/tEBYwOXBvIIRAaoBeoGIQbbBZ4F9AIJBOkDxP/u/hj+iPp1+oP8s/zj/Mz9Av5x//EBEwTrBWUF0ALsAG7/Zf4T/ZH72fif9eDzpvT39wX6mflS+sH7DfxZ/RUBkQF1AU4CggBr/2YAvv7v/Uz9rfsx+x38qPoA+UX6P/q++qT74Pyy/QP9dvwM/ZL+QgCVAKkAFgCO/lv7o/g+9yb1yfTw9JP0KvRa9M/1bfhy+oD89QACAR7/9f5r/gX+6fyU+7/5OvgR95j2Jvlp+fz4Avr4+a375f5CAQcD8AMkApwAnwBH/wb9lvxz/Nn5ivhV+iL6SPp4/pcCUgVJCIQKzgm5B0gFGAWPBJoCfgLuAYkAnv5l/rIAggGqAkYEmAXlBJ0DkwOZA+UE2QfMCPUIJAf5A0oD1QQxBr0GEQZZBBADXgJ/BNII9wrkCi4KjwjqBtwDTQPPBEIFmgJpAHgBHgKHAqUFDAjwBvcGMgk1CScHCAY4BcYDKwJ0ArkEcwOR/1v+vv4E/2wAAgFyAGv+7fxj/Uv/dAIfBDME2gJmAM8A7wA4AWgBhQDi/UT7NfpQ+Bz5evua+9n8DP51/s3/bAA5AL0C2QQ4BSMDOP6E+1X6qfh7+oT78vmm+Lb4T/oo/iIBJwKQA4MDkQLoAbIAFP/z/M37jvue+gj6K/su+8n55Prc/T7/eAG+AYcCxwJcAKv+Iv6I/mv/d//V/dH6w/iW+Jr7iP4Y/0v+Nv2w/Pr7PP1/AIIBfv4i/k/9o/zJ/Dv/TwDd/DT7sPyG/UX+RP9L/1z9W/tx+4z9mP6G/Yb96P7t/Kr8yACOAqUBbgGpBJcG4gQGBBwEZwJeAb0CfwCj/HX6E/mW/KT/S/85ACsBXwRqB1IJcQoOCs8IeQgWCAcHDgJRAg8BJf7h/m/9yv/SAOUAbQOBAukDXgbPCAwJWwmpCOIIbQfZAwMErwRfBAgBZP+q/ML5ffyY/lwAfv8/ARIFXAQxBnYHhggEC90KMweZA1EC3wASAb/9vfgS9m73xvyi/t/9Kf15AGIFZQlWDBUJVQV2BMkEUwM0/9b8zfvo+cb41/fM+XT7Kf0TADsBSwJJBBwFewULAhQCpQG6/4r/Afsf+eD3EPkk/H76A/2m/Zb8EwBGAOACPgIKAyIFMwCpAKv+sv3u/hX6APmU91/6s/xe+7D8F/wy/q0CLgKfAGH/jACMAIT/y/7a+/j5avvi/bb8jvsK/DT7Y/0x/wwAHwFCAMf/jwCY/pUA2f1q/Gr7gvlb+sr7Svxq+/L5dPsc/UsCuAHe/mX+P/7iAMgB6/4r/837AP0s/tf7J/wm/eX9Wf2K/Kr/JP+R/5r/zP0k/Ib8pQBk/9v+nvsa/O76BQHR/q/90/zk+kH/HACv/cj+Vf6+AZ8A5QBr/vr7nP0O+9r7rvpY+hb94fso/pT/bgIrAmIAYgHx/uACTAAj/a38y/of/oX+EP0L/477vP2u/pn9cv0M/UD8/PxD/Zr8gv5n/2v+7f/wAr8AZgR1AUYA4QGK/2/9AP1f+mf7AvrG+E77Zfqw/GX+yv9/AEoDwgBaAzcDVALqAh/+i/4E/238S//W/Ar4QPzI+tv+Ef+1/VT/jgHX/4cCZQFMABwAO/7s/d7+HP6h/5b8Y/09A88AlgOPAGT/OAFeAY4BMP1U/6r8vP0o/4r/tvxx/+r/VgAIARMA3wAI/w8BZf6u/3H/HP2lAL8AEf9UAuX9GwELAYIAfgKs/Q8B+v+CAKj+1f2B/xIB//0GAIwAT/7eAYcCEv4TAKUAO/9IAa/9S/5E/7j+HwE4/ikA4gByAIIB/gEy/nIBtQCP/dH+4f7V+i/+R/yG/Xb9OP9X/xgByAHRAQQCfgL1/isBxf01/oT7Hfwr+2383vrc/Xj+Ef+eAUIAiwHhAZwAHwHK/8X+2P6W/M38af1Z/Xr8zP0j/Zv+/f/R/r7/+/7t/zMAAAD7/qv+Yf9B//X+ov01/lP9h/+W/cz9wf7s/ff/5f4r/0v/cgBPAAYAAAB0/zYAlP/V/i/+r/3x/hX+Ef/J/TH/NgClAGUBtQD4AMEBYgGyAB8Awf++/8j+5P/P/SMAH/61AJf/6AAFAfwAnQIfAVYDdgAOAvIAnAA2AHYALv9cAOT/wgBWAFgBUgFHAlcCIQLOAcoCuwFLAvUASAHsAKUBvABpACIBKwEnAm4BRALkAeoCdAK9Av4BEQLbAasBeAEPAeUAGAFIAWIBuAHhAScCsQLtAvoCzQKUAnQCSwLYAXsBOAFFAUIBaAE7AbUB2wFOAqQChwLnAs0C0AKRAksCLgILAsEBoQG4AbEB5AH7ARECRAJnAmoCegJXAlQCNwIxAggC6wHIAcUBrgGxAa4BxQHeAf4BFwIUAiECGwIhAh4C8QHoAcUBsQGbAYsBggGeAagBtQHRAdgB/gH+AfEB7gHLAcEBmAGYAX4BggF+AYIBlQGxAcEBzgHrAegB+AHUAcsBvgGoAY4BeAF7AWUBawF+AYgBmwGlAbEBtQHFAbsBrgGxAasBmAGOAY4BiAF+AYgBiAGVAY4BkQGhAaEBmwGYAaUBoQGbAYgBkQGRAY4BfgGFAYIBaAGLAXIBYgFbAWIBYgFlAX4BbgGFAXUBeAFrAWsBXgFrAXIBYgFbAUIBSwFFAT8BQgEvAS8BNQE7ATUBMgElASIBIgEoASgBKAEbARgBKwEoATsBPwE4ATIBIgEoASIBEgELAQUBCwEPAQIBCwEIAf8A/wAFAQUB/wD1APUA8gDsAOgA5QDoAOgA3ADsAOwA2ADcANIAyADMAMgAyADPAM8AxQDSAL8AxQCyALUAogCvAKwArwCvAKwAvAC/AMIAvAC5ALIArAC1AKkApQClAJ8AqQCiAKUApQCfAKUAogCpAJwAmQCSAI8AjwCVAI8AkgCMAIwAggCJAIIAfAB2AHIAdgB/AHkAbwBfAGYAYgBiAGIAYgBfAGIAXABcAFwAXABSAEkAPwA8ADMAPAA2ADkAOQBCAEYAPAAzAC8AKQAsAC8ALwAsAC8AKQAmACMAJgAZABkADwAWAB8AFgAPAAYAAwAJAAAAAAD6/wAA+v/6//3/9P/q/+f/6v/n//T/8f/h/+f/4f/h/+H/4f/h/+T/4f/q/+3/4f/a/9H/0f/U/93/4f/a/9H/0f/R/83/yv/E/83/x//H/8r/yv/K/8f/yv/K/8r/x/+3/77/uv/E/83/x//H/7r/t/+0/8T/x//B/8H/x//E/8H/xP/E/8H/x//R/9H/1P/K/8r/x//H/8r/x//R/9f/1//h/+H/1//R/9f/2v/a/9H/zf/a/9r/0f/d/9r/2v/X/9H/1P/X/+f/4f/h/+T/3f/a/+T/5P/k/+T/5P/h/+3/7f/t//f/9P/6//T/8f/t/+H/4f/q/+3/7f/0/+3/9P/0//3/9//0//3/AAD6//T/9P/3//H/9P/9/wYAAwD9//r/AAADAAwACQAJAAMACQAGAAYAAwAGAAMAAAAAAAkAAAADAAAAAAAAAAwADwAGAAkADAAMABMAEwAPAA8ADAAZAA8ADwAMAAYADAAMAAwABgAGAAkADwAWABYADwATABMAFgATABYAFgAPAAYAAAADAAkABgAGAAYADAAMAA8AEwAPAAkABgAAAAAAAwAGAAMABgAJAAkACQAAAAkAAwAJAAkADAAPAAwADAAPAA8ACQAGAAMAAwAPAAkADAAGAAMADAAJAA8ADwAJAAkADAAJAAwADAAMAAwACQAMAA8ADAADAAkAAAAMAA8ADAATABMADwATAA8ADwAGAAYADAATAA8ADwAPABMADwAPAA8ADAAMAAYADAAJABYAGQATABYAEwAMAA8ADAAGAAYABgAMAAwADwADAAAAAAAAAAMAAwAGAAYABgAJAAYABgAJAAwAAwADAAkAAwD9//3/AAAAAAYAAwAAAAAAAAAAAAAAAAD6//r//f8AAAMAAAD9//f//f/9/wMAAAADAAAAAAD9/wAA+v/x//H/9P/3//r//f/9/wAAAAAAAPr/+v/6//T/+v/6//3/+v/0//H/9P/3//3/+v/6//H/8f/q//H/7f/t/+r/8f/x//3/AAD6//3//f/9//3//f/6//T/9//3//f/+v/0//r/AAAAAAMAAwAAAPr/+v/9/wwACQD9/wAAAwD9/wAA/f/6//3/AAAAAAAAAAD9//r//f8AAAMAAAAAAPr/+v/6/wAA/f8AAAMADAADAAMAAAD3//f/+v/9/wMABgAGAAAAAwAAAAAAAwAAAPr/AAAAAAAAAAD9/wAAAAADAAAA/f/9//r//f/9/wAAAwADAAAAAAAAAAYABgAAAP3/AAADAAMAAwAAAP3//f/9/wAA/f/6//f/+v/9/wAAAAAAAP3/AAAAAAMABgAAAAAA+v/9/wAAAAADAAAAAAAAAAMAAwAAAP3/AAADAAAA/f8AAAAAAAAAAAAAAAD6//r//f/6//3/AAD6//f/9//6/wMABgADAAMAAwADAAYABgAAAP3/AwADAAMACQAJAAYADAAMAA8ACQADAAAABgADAAYABgADAAAAAAADAAYABgAJAAwADwAMAAwADAAMAAkABgAJAAwADAAJAAYACQAMAAwADwAMAA8ADwAWABkAFgAPAA8ADwAJAAYACQAPABMAEwAZABYAGQATAAkADAAPABMAFgAWABYAFgAWABkAGQATABYAFgAPABYADwAPAAwADAAMABYAEwAPAA8ADwATABkAGQATAA8AFgATABMAEwATABMAFgAWABMADwATAAwADwAJABYAFgAMAA8AEwAPABYADwATAA8ADwAWABYAGQAWABYAFgAWABwAFgATABMAFgAWABYAFgAWABkAHAAZABMAEwAJAAkADAAMABMAFgAcABYAFgAcABwAGQAWABMADwATAA8AEwAWABYAGQAWABkAFgAWABMAEwAWABkAHAAZABkAHAAWABYAGQAWABYAFgAZAB8AHwAcABkAHAAZABkAGQAWABYAGQAZACMAIwAcABwAGQAZABkAHAAZABkAGQAcACMAIwAjACYAIwAjACkAJgAfABwAHwAfAB8AHAAcABwAHwAfACYAKQAjACMAJgApACkAJgAfABwAHAAcAB8AHwAcAB8AJgAjACkAJgAcABkAIwAcABwAIwAjAB8AIwAjACYAKQAfABkAHwAcABwAIwAjAB8AHAAfACMAIwAfAB8AIwAfABwAHwAfABwAGQAjAB8AHAAcABkAGQAZABkAGQAZABkAGQAWABwAHAATABYAHAAWABkAHwAjAB8AHwAfAB8AHwAZAAwAFgAWABwAHAAcABYAHAAZABkAGQAZABMAHAATABkAGQATAA8AFgAWABwAHwAfABkAGQAZABwAHAAWAA8AFgAZABYAGQAZABkAHAAcACMAIwAjABwAHAAZAB8AHwAcABkAHAAcABwAHAAcAB8AHAAjACMAHwAcAAwAEwATABwAHAAZABwAGQAfAB8AGQATABkAGQAZABMAEwAJAAwADwAPABMAGQAcABkAGQAfABwAIwAjABkAGQAWABwAHAAfAB8AHAAZABkAHAAZACYAGQAfABwAHAAZABMAEwAWABwAGQAcACMAGQAWABYAHAATAA8AFgAWABkAGQAZABkAFgAZABkAGQAWABkAHwAcABwAHwAcABwAGQAZABkAHAAcAB8AHwAjAB8AHwAcABYAHAAcABkAGQAZABYAGQAZABwAHAAPABYAFgAcABkAHAAWABYAFgATABkAHAAWABwAHAAfABkAHAAWAA8ADwAJAA8AEwAZABYAGQAcABkAGQAWABwAEwAWABwAHAAZABYAGQAWABkAFgAPAAkADwAPABMAEwAPAA8AEwAPABMAEwAMAAYADAAPABMAEwAJAAkADAAMAAwADwAJAAwADAAGAAYACQAJAAYAAwAJAAwADAAGAAYADwAMAAwACQAGAAYABgAJAAkADAAGAAYACQAGAAwACQADAAMACQAGAAMABgAAAAMAAAADAAYACQAGAAAABgAJAAwACQAJAAwACQAJAAwACQAGAAYADAAMABMAEwAMAAwACQAJAAYAAAAAAAAAAwADAAMABgADAAAAAwAGAAMAAAAAAP3/+v/9/wAAAAD3/wMAAwADAAAAAwAAAAAAAAAAAP3/AAADAAAA/f/9/wAAAAD6//T//f/9/wAA/f/3//f/9//3//r//f/3//3/AAAAAAAAAwD6//r//f/6//r/+v/6//T/9P/0//T/9P/0//r/9//0//T/9P/t/+r/8f/q//H/8f/n//H/9//3//f/9//0//r/+v/3//f/9//3//T/9P/3/wAA/f/0//T/+v/3//T/9P/0//T/8f/0//f/9P/t/+3/9P/6//T/9//t//f/9P/0//r/9P/3/+r/6v/n//H/9//q//H/9P/x//H/7f/t/+f/8f/3//T/9P/0/+3/9P/x//f/9P/x//f/9P/3//r/+v/3//f//f/9//T/9//3//T/8f/3//H/9P/9//f/+v/9/wAA/f/6//3/AAD3/wAAAwD6//f//f/6//3/9//3//3/AAADAAAAAAD6//f/+v8AAAAA+v/9//r//f8AAAYAAwAAAAAAAwAAAPr/9//3//T//f8AAAYAAAD6//f/AAAAAAAACQAGAAAABgAAAAMAAwAGAAAAAAAAAAMA/f/9/wAAAAAAAAkAAwADAAMADwAMAAwACQAJAAAAAAAGAAYADwAGAAkADwAMAAwACQAGAAMACQAMAAwACQAMAAkADwATABMAGQAfABMACQAJAA8ADAATABMADAAPABYAEwAMAAwADAAMAA8ADAAPAAwADwATABMAFgADAAMABgAPABMAFgATAAwAEwATAA8AFgATABMAEwAWABkAHwAZABYAGQAWABwAGQAWABkAFgAZABYAFgATABkAHwAfAB8AIwAfACYAFgAPAAwAEwAcAB8AIwAjACMAJgAfABYAFgAfABkAHwAsAC8AKQAsAB8AKQAmAB8AHwAZACMAOQA5ADkAMwAvADMALwApACMAHwAjACkALwAsADkANgAzAC8AKQAsADMAKQAsACYAIwAfABwAIwAmACwAJgAcABYAGQAZACkAKQAsACYALAAfAB8AHwAcACMAIwApACYAHAAjABYAHwAmACMAHwAcAAwAGQAcACMALAAmACkAMwAzACMALwA5ADMALAAzAC8ALAApACMAHwAzACkAHAApAB8AGQAjAB8AHAAmAB8AIwAZACMAKQAmAC8AKQAcACYALwAmAB8AFgAjACYAJgAzADYAPABGAEIARgAsACwALAAsACwALwAzACkAMwAjACwAHAAAAAYAHAAfADkASQBCAE8AVgA/ADYALAAvABwAEwAZAAYADAAWABkAKQAsACYADwAGAP3/AAAMACYAPwBWAGYAVgBMAEIARgBCADwAHwAZABYAHAAfADYAPAA8ACYADAAAAAMACQA2AEkAXwBvAFYATABZAFkAVgBPADYACQD9/9T/2v/3/xwATwCCAHwAaQA8AAkA7f/R/8r/6v8TADwAZgCZAJIAiQBWADMA+v/E/4T/h/+q//r/MwByAJkAsgDIAJUAPwDq/8f/sf+k/67/qv/K/7f/1/8TADMACQDd/4r/ZP9b/27/l/8GAFwAxQDiAI8AEwCu/3H/S/9x/7H/x//d/wAAGQAfAIwA2ADCAB8Agf9R/3H/mv8PAGIAwgBlAW4BGAG8AF8A3f8b/97+Z//N/zkAyADIATECAQKVAbUA0f+r/r/9mf0C/hv/PABOAcsBkQFoAdIAAABH/7v+vv7o/jj/yv+CAJUB2AFXAqcCVALuAQUBEwAx/4r/MwC/ADUBPwGxAUECMQKeAY8AEwCR/6T/ggBvAH4ByAF1AYwA/v5n/7T/MwAy/k7/FgDd/6T/ZP/PAOsBtwIiAR8B3ABSAUT/3P2F/hj/ZgAB/6L+3AAEAgQCuQA0AgYEVgDl/uUA0QGKApcCHAT0AsIAb/5O/zT/WP59/Hz9o/zBAWkEVwYUB1sFNgQIAQ7/W/tx+yD8sfr0+wP92v9uBoIE3QIsBKcC9QAY+4X6w/zB/xkEnv/3BrwIKQjKBvr3Bv079kn1g+z/6Lr7YgUbDg8R1BotIGMgB/xH44zhMODC3WPoAfPZCDsa/hYRFlgNdgNb+rfvwOec6QfwiPbX/44GFAu1DWwQUg0R/8H3vu5P6ffv/Oxt/O0LDBX1FbYM9wJO++XxsOt350305PumDEQTbhJxEk8MowO0+wT7wvXk9kb98vlOAuIIOwHOCYb14PjD9K3z1vAg+BMDYAPoCRAH9gO6At3//PDY9kYEgf80/yYA0QZXC9ALd/uuAWP4xf0s9W3wkvou/8QCaAXFBegJnwzxDSwINghuBXQCFv3t+5T3/P2y+VIJrv9yAKUFdwo3D9QC4AOk/xIBBgB095j6K/8c+ggFlgNHBhIJgggRCvL54fI25B7ui+6i9fsJyxGOMiEfrx2oCZ4BOu/75WbgHOmv9RL6AQYdDwYMZwoeBlb5VgRPAK/9rfvZ/Y4CdwJ38wvzxfrN+1f/U/jh+hIBCge5AwYEBwOvCAj/ZP/J+Nj+XwCXBugJzwh3CuoGUwSd/OTuW+rO6rn0//UO98L9zgHsBHn9C/uP+WP0EPjD7Jn1BfaY/l8JxgsFDrMPnQfu/uD4T/UB90P4S/fe+qAHswMmDE8FwwfV/rX+AABx+yoHSgfsA+sJGAq5BMsBmAWf9cUAa/8w9LkHM/kFAQr85f5ZABwAnACg/BYAZPt4/o38IAcwBxgF8AdXBvwAlQF6+DbtxfVK8En12flB/4YDkQrgBzADX/rk+nX2XPV+9qvybvpFAQQCnAAEB5T/YAP7AQr4UPR1/p77bfjX+y/6Pgp3BksBYgAA/OUAEv6k83n9XPUE/0T7EP3v9UcCAvr++lL6Tfix/1/5O/Zb/7L9jgFRAvT7rvpQ/J77TvM38zz1afmi/nL9dwKXBiEK1ALU/zj+Rfrx/mv/rv4a/Bz9a/qtAh38NwL/BMkI6galBesFjwhmAwT/lP+l+kH/bfgn8AL2YftNA3gBjQNnAqkD2AC4/nH/ef2gA0kA5//MAE4GEgVKB/EBygLLCTwEVwp6Bw0TqhKGDCcHCAmcACUFEgWY/nIIeQRh/2EC1f5/+Y732fzt90L+3gFLATUFKQT0AQwE1PsOAgv7Mf+HBkoDFA6tBo4GEAheAbH6JQFm+SP5JP8P/o4BvQOmCAAAEwCa/9f7HvusAKv+uwXaCloLbwnaBicGBgB7/9X+//2T/Cn9R/yA/JADAgXsAF8AaQQbAmf7B/xa95H7Bfr++gH/PAQmDMsB3ATv/Xb8I/jF+Y3v5fn4/UX+VA61BdgN7hG3BgoDbPmn/5z9mP5r/yECGAWVDQwJO/8o/4f/FgBh91n5vfdQ+J8EK/rd99wA3P3UAiH7wPiv+Zn9cfsn8wTzfvfZ+Cf0vATRDccGwgjK/yD9CP4Q+Wb9pQEMBTwMbwm9Ch8EQwOO9tj1RvFO9vD4rv5AB6oGqAnJBIUJ6vv0/63zJPcH+CP5Bf7m/MwEqAHR/3T3mfjI9TD8v/kU/07/bgWbBZP4ufmF+h/6APlg+Lv6nwDFBV8EGQBeBksBWQDJ+JL53AAE/7X9Yf9cBKcGtwbqAuj+fvoWAAPw0vVM/bL+wv2l/uIEXgEAAFv/FP//ALUBNgS/BG8AEwCS/qb97AC3AvkDfQdjC5EGtwY4/y/+ygYR/wsGcgEXA/MDGgOvBG7/NP8GAJv69wJD/GH/N/um/IUAg/wPCXr7SgdOCpoLpwYJ/YT/uAGrAZIA2QQXC0gOBgilAX/9M/23+9v6WfUA/c8I1A6hCsINwwsyAV8IQ/yN9C30lfVs9SH7WwF9A8IEGAViAewD6fxw/G760PwQA9X+R//JA/UE2fxH/3760PQU98r3+/5eBm8IBggNA5QG2QMf+bP40ALq9937Fv1L//II8QlPCFoHRwosBE4Bkvp49oz5ivsKAwwIsg0aCxQGSwXxBRr8cPg8+bUERgDLBRUBMgF0/+r/qvyT/DQCAPmpA8z9JAL+/ir8Qf/G+M/92v/2+F/+M/ig9If7RgBBBoADlQVjB+IERf6h99T7wf+lAe4BdQXoBYgN7gFLCeEFFwLr9uTzjfC48UD02P4EB+4FvgU3BwIBufzF+oz19O6lAOr/ovrr+sEBOQhqAlv/LPlI/rX6ivyX86D49AWgC0EGYwf8/bf/8QFq85f7CP66/8wIAAClAE4B4/xZ9Vb1fvofAdAC4gAI+58AWwF5ADP9OwJoAZcKRgAp/YgFPwU1/uL9Of3j/BYA0fIk/AQHKQRgC3YDogRnC8kIfv5b+z4CavjX8/L5Jv0iCQcLdgeYCRoH0QHL/k77Tvfu/i/20f6sADH3DgYACE4CnQfQAzP98gAE/wj+1PLx9tH69PtCAC4GogmSBGEC+vuq7+D34fta+O8ASvxsAMgAFf4FBXr8S/7b/vf3hv2L/tsFwQa0BuoKsAun/8sBVPve+lYAX/XV8WP4cvYG/eb86P6U/6X2QPTE7zTvN+/g9y4CyQxwD18QVwZpACv2G+687EDs3PXx/2YIhgumD8UJVQXB8nvq6fCS6bTz//kQBIQKov5PAMfv/Oyi6cbomOnG7M72F/QJ8aj2aPYzAL3zhfZl/hr8lf5q+2v/ov4HCxgKlAZECij+xPvV+Xf7v/X0+3j+DP5JCNwE1AogC3IAd/tV8uzwzfiMANwD3QIVBcb8lPs29TjujgK0Ao4FnQfOAQ8BIgEB9zfr6/IG+QkAHAitB4UNxhNfBHT7avRd8BL6OflH/+YHthTrFWAPXwifALP0avMW8QD0FgB3CpQOSg9FDdEFcweH/+jyrPWE+/z9YwOEDusZDReSEXsNggEZ+fD3b/lJ9YIAqAFYBRQOohADDKYMUQIL/3b8M/jH9+L11PuAAykElgQMDOoSOxI8COIEIwDhAaX6Zfao+j8AYwNKA+gAdgxhCmcGrQZP/df7K/Nf9eL1GgPxCdALzg6YCYIEdvmc/RjySvPY+pb4S/50AjQLQgkGALX9fvsQ+K7+SP5n/48ECQAk/+EBHATH/w4CEAP4ATECMgV2A3z5GQCa/LkAzgmfBBcLMQY3AuwDpfkI/rH6AP29/B30Rfr5+F34y/Yn9Mz1RfZN9Nf/tAbGC44Jzv5d/GDseOqB5tPk+PGi/e0GwAu5DAcHHvuz+NLtYuE/6R7jIPSJ/br/ggHRAaT/BPgz+U73wfem9K/1a/Z0+/QByQOv/c/5gPwL9zntRvSh8t33AANB/+wADwX4+WL2Dvch82/1j/l2/Hr8C/+qBlj+CAGL+pn1fvp392P4SvNi9mf3b/13+8EBLP2h/3n9A/W78onxnvYh+/n4SQD2B0oL8gwIAVH/BgD/+cPwNO/5+ED8LwQ4BWUJJwsJDPUEP/rF+rb4APxbAbf/1wL4BX38F/ju8nv2/Plr/goDuAWLCYoK2QS++nr7mfkY+hb97f+UBmUJoQZDCK4FHAkRAlv2lfaJ9Tv3+fyxBf0O7RtEFhgOsANe+4b1n/EK9ED4zgGJBEoHPBAKC80K8Qrn/x8BQPgj9Gb0GvhZAEYAIgVLCiINKA6iDQQCN/xG9RHzdvW49oL94PzjCxoPpRFhDncPHA2SBKsBo/iM+ev+pPtxBngNBAvhDoYLcwduBdn9YPzp/DL+aAGF/r0CjgUY/7wE1gMLAc76nv83Aj4CtQGQ/Mv+2vsn/Hb98QFN/IL+4PwL94b81f4OAsEBSgOXCjsJAAOTBxD9yved92v2rvr6+1IAfQNyCI8M+Ai+DowIDPpy9nP0R/dy+kv/uQeMDHYQXw1cCCUF5fn89YrzPvd4+okAqAnxEocXoA+wB7sBM/kP9UzxTPEy9qT7kwNwC0MQ+xEVDXkI8QWe+kfvZe467xT8PwTiDKsWnxTEFtEKpQXQA4n5FfYL90IA9Q3uDegR6xHHDusN0QZmBFv+jwAn/EP8ywG1ACUFzAiTB3oKGwYhAsz9PPlO+6n15Pcs+cH6VgNHA04GbgbyBCsFRQW9A2IJmA3XDtQS2w04BfIIzQf0CkgO+g5nCyYISwZ9A1H/TAD0BhAIwQp1DfALQgCe/6H7AACqAlz95AXVCCkMQwMEA6ECCP7990T/BgA3AvIEiAF2AKEG6wWlCbkMrAzRCZUFWgJIBeYDGwFxBn0DLwRaB8EBjAAu/2D4EP1tA14B1AJTBBQGPwRrAXADBAbxBWv+8gBaA4T/DAVdA4oCUgEXArX99PpR+7b8RPv4/dYHpgPB/5j+zfc3+FL2CfnT9L33bfyJ/fQC2/6D/K/9v/mY+qb4dfa8/c7+Kvwz/GH/cftO+2T77v5+/pX2S/fy+U/51f1h+177H/qZ+fv6m/ql+jb9tf2s/bH/ggGx+r30ef0O+xz9pv2y/X7/+/pj+NH33P2q/9v6X/1D/Kb9aP5z/LsFUAMsAD776Pai+jv2jfP0+4r/GAGtAi7/s/x5ABMAvv+tA84B0QmOCskHdALP/UP9C//lBJkAJwNyCAMIwgSfBFT/CQDlAEP8BP9iAaEB3ATZAzj+dQFmBNf/Z/8dAxMDvAA2/bv+9f60/3X+Pv/IATYICgt/BM4FsAeOCWkI5wJ9A4YDHQMrAaYDkgQzB2QKIgkGDGcKVwcAAwL+9vha+Jj2M/z0/4L93gHJBG8EQwdaB5r/BQG/AKX+xPvg/HX+aQDsAy8EsAcwB5sFngZ3Avj+JP8G+U/+mv/RAZgJJwP8AGECjwAl/jv+xP/l/YUAvgEL/z8BtPt7+vb4sfOZ+Dn9ZAJMBFoDUwcjA57/0fer9mL2tvhj+E72O/8nA9IA5AVQB+sFOgPD+EHz5O6R78bwbfQG+cH+wgCvBDcCYPyC+k77afk79/H2I/X4+hD8xP+wA+sBIwN4ARn9LP0s/gL+8v0c/UwA8/w7/jH/a/82/Yf/IwDj/Bj/f/3f/SH/8gBFAZH/Cf34+cn80vmN+HD8W/8YBf8AaQAwA5sBUgBPACz+Dv+I/i/+rv7u/jv/yABX/Jr8oPzB/1kAtgMyBfoCXwTRBQsBBgDs/e38IwADAHYDtAYGCOQG3gWF/uX+9Pox92L2kvkr/4sBEgXZB8wMIQ4UByv/L/4b+zr8G/t//Y8EDgplCeIIigd0BiMDl/sO+zb9qf3xAaoCBgSpA20DywF7/sIAdP/sAMcCXgLPBJkE1QVbBf8EFALq/wz+1f2S/W77xgMiBeoGzAj0BRIFIwTl/bT7Xfw8AMgB1gPpB4IFdwq1CP0GZQUpAK7+u/pJ+ez58/xCADYEXgU1BbgFvgWLAYUAe/5y+YT70foF+ub85Pog/DsB8AN/BCMEnQKmA7H/jv/CAFUBoQIxAgsBKQCIAUIBSQAMBWkEMQaMBJwAof/9++H6ifmW+Pz8FP9ZBJEGTQdHB8oCmvuf/YT7N/ia+2D8dP8EA1YEvwSZBIoCHgIvAWUBFAIRAgwAwf5RAoYEyQMVBQ8BRQVPBO8A+QNAA1MEsQKFAf4BwQJlAfH/qQAIAfcCtAIe/7T/CAJRAvIAQwMABHsFFwefBDAH1wIy/gv/GP4jAGECVwKUAm4C3QLeBe4FpgN2BBwEnQMjA4ADlgNpAG4CAQI4BSUJ2gIFBToHqQMIBpUFPgKdA6oCKgPiBB4GBAZuBWcGRQWeBnAHWwWnAk4C1AJ6AzADmQD5A+0CSwUGCMQCDwArAd/9Uv7uAXEC8QX8CPgFJAY7Aqb8GPtr+l/9JALyBIYH3QZlBcMD6gJF/gf8DP0R+37+kQFnAqQC2wEQA5QCbQPsAMn8wPyg/Ib8tvzm/Gf/1AHEAqgFogSsBGAD+P4A/Fv7svno+n/9R/90AjQCmgKpA44CeAGFAU4BewHPAHIBGP+U/xgBsgAE/3/9ogBvAMUBof8TAFsBhQBu/6v+R/93/wz+Gf0n/HH/vv++/wj/tfqE/yQC7f8AAJwAQ/0U/A77nvue+4T7Qfun/7UB0QEiAcz97fuO+pX6H/qD/KL95f7VAK7+PACk/w77cvpn++r7G/ui+tn54ftpAO8A9/8o/tv+3P2x+xX60foX/KD8ywE1BUoHPwW0Ak8AFv13+0D4Zvmb+lj++ASnBrYHLQeyBHH/OPqa+xb5+Pn3+538cAPFBS0DQQLNAuIAAf8l/lj6W/pq+wT8IP2R/xADAAcyBQMEbgYABLUAFf7t+2r85P+U/98ACAWjB2AHRQVxAo8Ab/7F/az9Uf+YAbcCoAO9AlME/QIrAhkAwPz3+/T7Y/34/rsBjAR2A7wEcgUzA+wAaf1c/VD80Pz1/vQCfgaOBqEGVAbJAwIBC/+y/m4BGAGrAXADdwLXAlECPwGbAWIB6AAPAIwAPwGpACwAiv/FAF4B7gEbAdsB/gFwA4ECKwJnAsUALv9M/fP8gPxM/aD80v0fAJEBswN+AakAMwDO/gT/ivxg/JD86P5yAMT/dwIKA5kECgNFARwAvP2q+7H6Afsg/Gv/aAHKAnYD7APUApEB+P6j/Ab9jP1Q/O38BP/b/uT/O/9M/dL9Yvq5+W77hPtx+wf8pP//ACwAhQFCAE8AO/6z/Hz9dvwj/ej+OAGCAbsBnQKMAPH+1f2W/Zf7M/yJ/bz9LP52ADQCBQHn/0/+1f4y/u37cvmg+B/5vvqX+zn9+P5VAdcC7AAAAFL++P1D/OT78/yi/Rv/qv8zAH8AkgB/AKwA2/49/Lb8wPxa/Bj7wfu5/Nz9L/6s/UT/Tv80/0v/mP4r/xj/y/4M/k/9T/7V/cn91f0V/rv+3v4TAKH/WP7l/iH/1P9yAP3/TwBvAEH/Nf6m/ez9pf4F/iX++P5B/07/+P6X/4r/fv4Y/kb9nP2c/Sj+xf5v/iMAsgA7//H+T/4U/57/j/3g/Nz9Tv91/jX+W/9r/9r/6f1A/GP9tf1+/oX+lv0c/uX+L/5r/8f/ZgAXArsBpQCo/gX+jP1Q/FP8T/2e/hMAcgAiAR8BrAA5AJL+Gf3D/Jz9P/5v/Qn9fP3V/az9gv7h/v7+8f5y/hj+HP16/OH7wPz//Q/+9f1J/XL9b/0J/Uj+CP+a/3kAPv/M/T/9Y/2//Wb9LP7N/0gBcgCO/gH/q/6C/V/9hvwK/J38Lfy5/Bn9AP3l/cv+VP++/tX+Jf6W/BD8FPsL+/r7HfzF/aL++P5PABYALv8f/n/9Zv2j/PP8E/15/d3/fv+a/9H/Uf/R/1H/hf7J/Gf8mvwT/VP9rP1F/r/9D/7V/s7+ov4F/iz+zv4E/xv//v7B/oz9c/zd/ND8Zv2I/uv+BQEEAtEB7gFJAJX+Ff4P/gX+if3v/Xv+JgDFAYUBrgF7AdIA5/+C/mz97P1v/hH/0gChAZECugLyAH7/kv7L/rX+e/6b/jT/NgBmAIIAxQBFAV4BzwBWAJ7/x/97/0T/9/8jAMUBCgM9A20DTgKCASMAu/5l/pn9/P0L/wkAYgG+AYcCXQNxAmIBhQB/AE8Aqv+6/x8A9QCsANIALwFLAf8A0gDVAHwAYgAzAHf/FP8h/67/GAFCAQQCcANwA+QC+wEYAYT/WP7v/RX+Hv8GAL8A3gHBAsECkQLhAaIAvv+r/jv+yP4Y/+T/0gCOAesB3gGLAX8Aqv/R/kL+nv4E/8f/vAASAfsB0AKUAjcCggHMAFkAyv8I/wT/yv8cAI8A7wDMANgAWQA2AEkArACsAJUA4gCFAGIAbwDB/2T/R/80/1f/a/9O/2H/AACFAO8AXgESAbUANgCH/xj/2/67/kH/LADCAG4BBAKrAVUBvwA2AKr/Af+1/s7+Z/+X/8r/yv+k/8T/ZP9B/3H/kf+u/8T/zf90/zH/+P7F/lT/wf8pAHYAjwB2AFkAAACR/1T/Mf9n/4r/hP+3/9T/1P/X/9T/vv/E/67/uv93/zH/RP9E/0f/NP9n/5f/kf+H/zv/+P7F/pj+gv54/sv+Uf+R/yYA1QBIAWUBHwFvAAAAmv9L/zH/JP9r//f/IwA5ADMA9P/U/93/yv+R/57/h/93/3v/dP+K/2v/fv+x/+H/9P/U/5f/bv+u/wkAJgAMABkAIwA2AGkArADsADsBbgGFAVsB1QBmABYAEwBJAIUAvwAYAXUBqwGOAUIB3ACCACkA9/8GACkAUgCMAAIBTgFiASsB8gDlAKwAfAAZAPf/BgAsAHwAtQAPAVsBngGuAaUBewE4AbUAeQB8AGYAYgB5AMwAQgGeAdQB5AGuAXIBGAGZABYAp/9n/4T/1/9PALIACAFeAZ4BtQFuAeUARgC3/z7/C/8Y/3T/AwCVACIBhQGFATUBsgAzANH/e/9R/3T/0f9WAMUA/AAFAdwAggAWAJ7/Tv8x/zT/d//U/0YAnACyAKwAnwB8AEIA5/+K/0f/GP8r/1H/lP/k/ywAcgBvAGkARgADAMH/bv87/xv/GP8b/zv/bv+x/9f/9P8MABMA3f+q/17/Hv/4/uH+Af8o/1v/h/+q/83/1//h/+H/t/+B/1T/JP8O//X++P4U/0f/bv+a/5r/jv9u/zv/Ef/l/s7+yP6+/rv+3v77/iH/Qf9R/1f/Uf8h//X+u/6l/pL+kv6L/p7+wf7e/vH++/4I/xj/Hv8Y/w7/Dv8Y/yH/JP8k/zv/NP8e//7+1f61/p7+lf6e/rL+0f7u/hH/If80/yT/CP/l/q7+fv5i/lL+Yv6I/rL+1f74/gT/+/71/sv+q/6Y/ov+e/5y/pL+u/7R/v7+GP8R//v+4f6i/mj+SP4f/hz+Rf5r/pL+vv7h/u7+8f7I/qX+gv5S/iz+KP41/kv+Zf5+/o7+qP6l/o7+e/5b/kL+Nf4i/iL+Mv47/j/+Uv5Y/lj+S/5C/i/+HP7//fj99f3y/e/99f38/Qz+GP4V/hX+D/4Y/gL+/P0C/gL+Av4M/g/+Bf71/ez96f3c/dX92f3i/eX98v38/fz9Bf4F/gL+7P3l/d/90v3M/cn90v3c/eX98v0F/gz+Ff4F/vz98v3p/eX97P3v/fz9Av4V/hX+Iv4f/hz+Ff7//fL96f3f/d/95f3y/fz9D/4Y/h/+KP4o/iX+KP4l/iX+Jf4f/h/+KP4v/jv+Qv5F/kX+S/5I/kj+S/5F/k/+Vf5i/mv+cv57/nX+fv54/nj+a/5f/l/+aP5r/m/+iP6L/pX+m/6i/qX+nv6e/pL+lf6O/pj+qP6l/rL+y/7b/t7+5f7u/u7+9f7u/vX+9f7x/vH+Af8B/wj/Dv8O/xT/If8r/zH/NP84/zH/OP84/zv/O/84/zT/S/9R/1H/Yf9k/2H/a/9n/3H/dP9u/27/e/9u/3f/fv+E/4f/lP+U/5T/l/+e/5r/nv+a/57/of+k/6f/p/+u/67/tP+0/7f/t/+6/7r/uv++/8T/wf/N/83/2v/X/9r/5//t/+f/5//q/+r/6v/3//r/AAD9//f//f/9/wAAAAAAAPr//f8AAAwADAADAAwADwAGAAAAAwADAAMACQAJAAMACQAJAAYACQAJAAkACQADAAAADAAMAAYADAAJAAMADwAMAAwADwAJAAMAAwD9/wAAAAAAAPr/AAAAAAAAAAAAAP3/+v/0/+3/8f/x/+f/6v/0//T/9P/9/wAAAAAAAAAAAAAAAAMAAwAAAAAAAwADAAkABgAJAAYACQAMAAYABgAGAAYABgAJAAwADwAMAA8ACQAPAAkADwATAAwABgADAAYAAwAGAAkADAAPABMAEwAPAAwADAAMAAYAAwADAAwADAAMAAkADwAMAAwADwAJAAMAAAAJAAkACQAGAAkADAAPAAkADwAMAAYACQAPAA8ADwATAA8AEwAWAA8AFgAcABwAHwApACYAJgAjACYAKQAvACkALAAmACkAKQApACkAJgAjABwAJgAcABkAHAAZABYAGQAZABYAGQAcABYAHAAWABYAGQAWAA8ADwAMABkAGQATABMAHAAZAB8AIwAcABkAGQAZABwAHwAfAB8AHAAfABwAHwAfAB8AIwAjACYAHAAcABYAFgAZAA8AFgAcABkAFgAWABMAFgATAA8AGQAZABYAFgAZABMADwATABMADwATAAwAFgAWABwAFgAWABMADwATAA8ADwAPAA8AHAAWAA8ADwAMAAkADAAMAAwADAAZAAwADAAMABMADwATABMAGQAWABYAFgAWAAwAEwATABkAGQAWABYAHAAZABkAFgAcABYAHwAcACMAHAAfAB8AHwAfACMAIwAfABkAIwAmACkAHwAmAB8AHwAmACYAIwAmACkANgAvADMANgAzADMANgA5AD8APwBMAEwASQBJAEkASQBJAEIATABPAFYATABZAFkAWQBZAFwAXABfAGYAZgBmAGkAaQBmAGIAaQBsAGwAbABsAG8AdgB5AHwAfAB8AHkAfwB/AIkAggB/AH8AggCFAIwAkgCSAJIAmQCZAJkAnwClAKkArACsAKkArACyAKwAsgCsAK8ArwCyALIAsgCyALIAtQC5ALkAtQCyALwAsgCyAK8AsgCyALIAuQC5ALwAvwC/AL8AvwDCAL8AvwDCAL8AuQC/ALwAvwC8AMIAvwDCAMIAuQC5ALwAvwC/AMIAvwC/AL8AvwDCALwAvwC8ALwAvAC/ALwAtQC5ALUAsgC1ALUAtQC5ALUAtQCvAKwAsgCyAK8ArACpAKwAqQCsAKUArACpAKkApQCpAKUApQClAKUAnACcAKIAogCfAJkAnwCcAJ8AnwCZAJwAlQCVAJIAmQCSAJIAjwCSAJIAjACJAIUAiQCFAH8AfwB5AH8AhQCFAIIAggB8AHYAeQB8AHkAfAB8AIIAfwCCAH8AdgByAHYAeQB2AHYAcgBsAG8AbwBsAGkAbABsAGwAZgBiAFwAXABZAFkAWQBZAFkATwBMAEwARgBJAEYARgA8AEYAPAA5AC8ANgAvADMALwAvACwALAAvAC8AKQAsACYAHwAfABkAGQAZABkAEwATAA8AEwAPAA8AEwATABYAEwATAAYACQAMAAkAAwAJAAYAAAAAAP3//f/9//3//f/6//r//f/3//T/8f/k/+f/7f/t//H/8f/q/+r/7f/q/+f/6v/q/+r/5P/k/+H/3f/h/93/4f/d/9r/1//N/9H/1P/U/9f/1P/U/9T/1P/N/8r/xP++/8T/x//N/8T/xP/B/8T/xP/E/8r/x//N/8f/xP++/77/xP/B/7r/wf++/8H/t/+0/7r/vv++/8T/xP/K/8T/x//H/8f/xP/H/8f/x//H/9H/0f/N/9H/1//X/83/0f/U/9H/1//d/+H/4f/k/93/4f/k/+T/4f/k/+H/6v/k/+T/5//n/+f/7f/t//T/8f/0//f/+v/9//r/9//6//T//f/6/wMAAAAAAPr/AAAAAAAAAAD9//3/AwAAAAMAAAADAAAABgAJAA8AEwATAA8AEwATABwAGQAfABwAHwAfAB8AIwAjABwAGQAcACMAJgAsADMAMwAzADMAMwA8ADkAPwA/AEkAPAA/ADwAPwBCAEkATABMAEkAUgBPAFIATABSAEwASQBJAEwATABSAFIAXABZAF8AXwBfAF8AXwBiAGIAXwBiAGIAXwBiAGIAYgBmAGIAZgBmAGwAYgBpAGYAaQBpAGwAbwBvAHIAcgB2AHYAeQB5AHkAfAB5AHwAfAB/AIIAfACCAIIAfwB/AH8AggB/AIIAfwCCAHkAfAB8AIUAhQB/AH8AggCCAHwAhQB/AH8AggCFAH8AggCCAH8AhQCJAIwAiQCFAH8AggCCAIIAggCFAIkAiQCFAIwAiQCFAH8AfwB/AHwAfwB8AH8AfwCCAIUAhQCJAIUAfwB/AH8AfwB5AHwAfwB/AH8AfACCAH8AeQB5AHwAdgB2AG8AdgBvAG8AcgBpAGwAbABvAHIAcgByAG8AaQBpAGYAZgBmAGYAZgBmAGYAYgBiAFwAXABcAGIAYgBZAF8AXABcAFYAWQBPAFYAVgBWAEwATwBMAE8ATABPAEwATwBMAEwARgBJAEIAQgBCAEYAPwA/AEIAPwA8ADwAQgA/ADwAOQA5ADwAMwAvAC8ANgA5AD8AMwA2ADkANgA2AC8ALwAvACkALAAjACYAKQApACwAKQAmAB8AJgAmACYAIwAcACMAIwAjAB8AHwAcABwAIwAjABwAHAAcABkAHAAZABMADAAPAA8ADwAMABYAFgAWABYAGQATAA8ABgAAAAMABgAGAAAAAwAGAAAAAAADAAMAAwAGAAYACQAGAP3//f/3//r/+v/9//3//f8AAAAA/f/9//r/AAD6//f/9//0//T/6v/q/+f/6v/t/+3/8f/t/+3/5P/d/+H/3f/a/+T/5P/k/+3/5//n//H/8f/0/+T/5P/d/+T/3f/d/9T/2v/k/+H/2v/a/+H/4f/d/93/2v/d/9H/1P/N/9f/2v/a/9f/1P/N/9H/1//X/9H/2v/X/9f/0f/N/9H/0f/U/9T/0f/N/8r/xP++/8f/0f/N/83/0f/N/8f/0f/R/83/yv/H/8r/zf/K/8f/x//E/8T/xP/H/8T/xP/K/83/zf/H/8T/xP+3/7r/wf/E/8T/x//H/8f/xP/H/8H/xP/B/8r/yv/N/8f/xP/B/8r/yv/H/8H/xP/H/8T/wf/B/77/xP/B/77/xP/H/8f/xP/E/8f/yv/N/8f/x//E/8f/x//H/8H/x//H/8f/yv/N/83/zf/N/83/zf/N/83/0f/N/9H/1P/U/83/yv/K/8r/yv/K/8f/xP/H/8f/zf/H/83/zf/B/8H/x//E/8T/wf/H/8f/xP/K/8r/zf/N/9T/zf/U/9H/yv/K/8r/0f/R/9H/1P/R/9T/3f/a/9T/1//R/9T/1P/X/9T/1//a/9r/1//d/9r/1//U/9f/2v/d/9r/2v/h/+T/5P/k/+T/4f/d/93/5P/d/+T/3f/U/9r/4f/d/93/5P/n/+f/5//n/+T/4f/h/9r/4f/h/+T/5P/h/+H/5P/h/+T/5//k/+f/8f/x/+r/7f/n/+3/8f/x/+3/7f/x/+r/7f/0//f/9//3//f//f/3//H/9P/t//H/9//6//r/+v/0//H/9//x//H/9P/0/+3/9P/x/+3/7f/t/+r/9//x//r/9//t/+r/9//3//T/8f/x//H/8f/0//f/+v/x//H/9//x/+3/9P/x//H/9//3//H/7f/3//T/9P/0/+3/7f/x//T/8f/0//H/9P/x/+3/7f/t//H/7f/n//H/5//n/93/5//k/+r/8f/t/+r/7f/n/+H/5//q/+f/5//n/+T/6v/q/+3/7f/q/+T/5P/n/+T/5P/h/+T/6v/k/+H/5P/k/+H/5//q/+f/5P/q/+T/5P/h/+T/5P/k/+T/5P/n/+T/5P/h/93/4f/d/+H/3f/a/+H/3f/d/93/4f/h/+H/6v/q/+H/3f/k/+T/5//t/+r/6v/q/+T/5P/k/+T/3f/h/+T/6v/q/+3/6v/q/+r/8f/t/+f/5//h/+H/5P/k/+H/5//n/+H/5P/n/+f/5//t/+r/7f/q/+r/6v/t/+f/5//k/+f/5P/k/+3/6v/q/+T/5//q/+3/6v/x//T/6v/x//H/5//n/+T/5P/k/+r/9P/x//H/7f/n/+r/5//q/+r/7f/t/+3/9//6//T/9//0//f/+v/6//H/9P/9//r//f/9//3/+v/9//r/AAAAAAMA/f8AAP3//f/6/wAAAAD9//3/AAD9//3/AAAAAAAAAAD6//3/+v/9//r//f8AAAAA/f/9//r//f/6/wAAAAAAAAAABgAGAAkACQADAAYABgAGAAYACQAJAAkABgAMAAwAEwAZABYAEwAPABYADwAMAAwAEwATABkAGQAWABMAEwATABYAEwAZABwAHAAfABwAHwAZABkAHwAcAB8AJgAjAB8AHwAjACMAIwAjACYAKQApAC8ALwAsACwALAAsACkAKQAsACkAKQAmACkALAAsACkALAAsACwALAApAC8AMwAzADYAOQAzADMANgA5ADwAOQA5ADwAMwA2AC8ALwAzADYANgA2ADMAMwAzACwANgA5ADkAOQA2ADYAOQA2ADYAOQA2ADYAMwAzADYALAAvACkALAApADMALAAmACwALwAsACwALAApACkALAAsACwAKQAvACkALwApAC8ALwApACMALAAvACwAIwAjABwAHAAZAB8AJgAmACYAKQAmACMAJgAfAB8AHwAfABwAGQAfABkAGQAZABkAGQAZABYAFgAWABkAGQAPAAwADwAJAA8ADwAMAA8AEwATAA8ADwAMAA8AFgAZABYAEwAWAA8ADAAMAA8AFgAPABMAEwATABMAEwAPAA8ADAAMABMAEwAPABMAEwAPAAwADAADAAwADwAPAA8AEwAPAAkABgADAAMABgADAAYABgAJAAYAAwAJAAYACQAJAAkACQAMAAwADAAGAAkADAAMAA8ADwAWABMAFgATABkAFgATAAkADwAMAAkACQAPABMAEwATAA8AFgATABYADwAPABMAEwATAA8AEwAPABYAHAAcABwAGQAWABkAGQAcABYAGQAZABkAGQAcAB8AHwAcABYAFgAWABwAHAAWAB8AJgAjAB8AIwAZABYAGQAcAB8AHwAfABkAHAAZABwAFgAcAB8AHwAZABkAHwAcAB8AHAAZABwAFgAWABkAHwAcABYAEwATAA8ADwAPABMAEwAZABYAFgATAA8ADwAJABMADAAPAAwADAATABYAEwATAA8ADwAMAAwAEwAPABYAGQAWABMAEwAPAA8ADAAPAA8ADwATAA8ADAAGAAYABgAJAAkABgAJAAwADAAMAA8ADAAJABMAEwAPAAwADAAJAAkACQAMAAMAAwAMAAkABgAGAAkADAAJAAwABgAAAAAAAAAAAAAAAAAAAAAA/f/3//r//f8AAP3/AAAAAAMAAAAAAP3/9//3//r/+v/6/wAAAwAAAAMAAAAAAPf/+v/6//3//f/3//r/9//0//r//f/9//f//f8AAAAA/f/0/+3/8f/0//T/9P/0//T/8f/n/+r/7f/t/+3/7f/x//H/9P/0//T/9P/0//T/9P/0/+r/7f/t/+r/6v/q/+f/5//n/+f/5P/n/+f/5P/h/+H/4f/k/+H/4f/a/9f/3f/a/9r/3f/a/+H/5P/n/93/2v/U/9T/2v/a/9T/2v/h/93/2v/a/9f/2v/X/9f/3f/d/9r/1//R/83/0f/X/9H/1P/X/9r/1//U/9T/1//a/9f/2v/a/9f/1//X/9r/1//R/9r/2v/X/9H/2v/X/9H/0f/R/9H/0f/U/9H/0f/U/9H/1//X/9f/1//R/9T/1//U/9H/0f/X/9T/0f/R/9H/zf/N/83/0f/U/9f/1//a/9r/2v/a/9f/0f/U/9f/3f/a/9r/2v/U/9f/2v/d/9r/3f/k/93/4f/k/+H/4f/k/+T/5P/h/+H/4f/k/+f/6v/n/+f/5P/n/+r/6v/t/+3/8f/x//H/8f/x//T/9P/0//T/9P/0//r/+v/6//r/9//3//H/7f/q/+f/7f/x//T/8f/x//H/9//3//f//f/0//T/9P/0//H/9P/3//T/+v/6/wAAAAD6//r//f/9//r/8f/3//H/8f/x//f/+v/6//r/AAD9//f/9//x//H/9P/3//T/9//9//r/AwAAAAMACQADAAAACQAJAAMAAwAGAAYACQAGAA8ADwAMAAkAEwAMAAwADwAPAAwADAAJAAkADAAPAA8ADwATAA8AEwAPAAwACQAGAAYACQADAAkACQAPABMAEwAMABMAEwAZABMAGQATABMAGQAWABMAEwATAAwADwAPABMAFgAPAA8AFgAZABYAFgATAA8ADwATABMADwAJAA8ADwAMAAkADAAJAAwAFgATABMADwATAAkADAAJAA8AEwAPAA8AFgATAAkABgAJAAYADAAMAA8ADwAJAAYACQAGAAkADAATAAwADwAMAAkABgADAAYACQAGAAYACQAJAAMABgAGAAYAAAAAAAAAAAD9/wAA/f8AAAMAAwAAAP3/AAD9/wAAAAAAAAAAAAAAAPr//f8AAAAAAAAAAAAAAwAAAAAAAAAAAP3/AAD9//r/+v/6/wAAAAAAAP3/AAADAAYAAwADAP3/AAAAAAAA/f/6/wAA/f8AAP3//f/9//3/+v/3//f/+v/6//3/AAD6/wAA/f8AAPT//f/3//f/9//9/wAAAAAAAAAAAAAAAAAAAAAAAAAA/f/9//3/+v/6//3/AAAAAP3/AwAAAAYAAwAMAAMABgAAAAAAAwADAAAAAAAAAAMAAwADAAAAAAAAAPr/AwADAAMABgAGAAAAAwAAAAYABgAAAAAABgADAAMABgAJAAMABgADAAMAAAAAAAMAAwAGAAMABgADAAYACQAJAAYAAAAAAAAAAAAAAAAABgAGAAkABgAGAAMABgADAAYADAAPAAkACQAJAAwABgAMAAkABgAJAAMACQADAAYACQAPAAwABgAJAAYAAwAAAAMABgAGAAYACQAGAAMACQADAAAAAwADAAMABgAGAAkABgAGAAYABgAGAAYABgAGAAYACQAGAA8ADAAJAAwADAAJAAkACQAPAAwAEwATAA8ADAAMAAwADAAMABMAEwAPAAwAEwATAA8ABgAMAAYAAwAJAAYABgADAAkAEwAMAA8ADAAGAAkABgAJAAYACQAPABMAEwAZABkAGQAWABMAFgATAA8ADAAWABYAFgAWABYAEwATABkAGQAZABYAFgATAA8AFgAWABYAEwAPABMAFgAPAA8AEwAMAAkADwAPAA8ADwAJAA8ACQAJAAwADAAJAAwADwAPAAYACQAGAA8AEwATAA8ADAAPAAkADAAJAAwACQAJAAkACQAMAAYACQAGAAkAAwADAAYAAwAAAAYABgAJAAYADAAGAAkACQADAAYABgAJAAMAAwAJAAMAAAADAAAAAAD9/wAAAAAAAAYAAAAAAAAAAAAAAAAA/f8AAPr/AAD9//3/AAD9//r/9//6//f/9P/3//r/+v/3//3/AAD9//r//f/x//H/+v/6//f/9//0//f/9P/3//f/+v/3//3/AAD9//f/+v/6//f/7f/x//H/9P/0//T/+v/6//f/9P/t//H/7f/x/+3/9P/t//T/9P/0//H/9P/0//H/9//0//H/7f/t//H/9P/0//T/8f/q/+f/7f/0//H/8f/x//T/9//x//T/8f/t/+3/9P/x/+r/7f/n/+r/7f/q/+3/7f/0//H/6v/x/+3/7f/q/+r/8f/x/+3/7f/t/+r/7f/x//H/8f/t//f/9P/0/+3/8f/x//H/7f/q/+f/5//t//H/7f/x/+r/6v/q/+f/6v/q/+3/5P/k/+f/6v/n//H/9P/x/+3/8f/x/+T/5P/k/+T/5P/n/+r/5P/t/+f/7f/n/+T/4f/d/9f/4f/d/+T/5P/d/+H/5//k/+T/3f/X/9r/5P/k/93/5P/k/+f/5//n/+T/5P/k/+H/4f/h/+H/4f/h/93/2v/a/93/4f/a/93/5P/d/9r/2v/U/93/2v/k/93/4f/a/93/3f/h/+f/4f/k/93/3f/a/9r/4f/a/9T/3f/a/+H/1P/U/9r/2v/a/93/4f/k/93/4f/h/93/2v/d/+H/4f/h/+f/5//k/+f/7f/q/+T/5P/q/+f/5//q/+3/6v/t/+r/7f/q/+3/6v/q/+3/9P/x//H/9P/t/+T/8f/t//H/8f/0//f/9//3//T/8f/x/+3/8f/t//T/9//3//H/9//0//r/9P/x//H/9//3//f/9P/0//T/9//3//f/+v/9//r//f/9/wAAAAADAAAAAAAAAAAAAAAAAAAA+v/9/wAA/f/9/wAAAwADAAAAAAADAAAAAAADAAYAAAADAAAABgAJAAkADAAGAAYACQAGAAkABgAPAAYAAwADAAkACQAMAAkACQAGAAkABgAJAAkAAAAGAAYABgAMAAwABgAJAAYABgAGAAYABgAJAAkABgAJAAkADwAJAA8AFgATABYAEwAPAAwADwAPABYAHAAWABYAGQAZABkAFgAcAB8AHAAcABwAHAAWABkAGQAWAA8AEwATAB8AHwAWABkAGQAfABwAIwAfAB8AHAAjABkAFgAWAA8AHAAcACYAJgAjAB8AIwAfABkAHAAfACMAHwAcACMAHwAcABkAGQAcABwAHwAZAB8AHAAcAB8AHwAfABwAGQAZABkAGQATABkAHAAcABwAHAAfAB8AHAAZAB8AGQAcABYAHwAZABYAHAATABMAHAAZABkAHAAZABYAEwATAA8ADwAPABYAGQAWAA8AEwATAA8AFgAZAB8AHAAWABwAFgAZABMAGQAMAA8AFgAcAA8AEwATAA8ADwAWABYAGQAWABkAEwATAAwADwAMAA8ADAAMAAwACQAMAA8AEwAPAAkAAwAJAAwABgAGAAkADAAMAA8ABgAJAAkADAAJAAYACQAMAAkABgD6//3/AwAAAAYABgAAAAAABgADAAwABgAAAAAAAAAAAAMAAAADAAAACQAMAAYABgAJAAMAAwAAAP3/+v8AAAAAAAD9/wkABgAGAAkADAAGAAMAAAD3//r/+v/6//r/+v8AAPr/+v8AAAAA/f8AAAMAAwAAAP3/9//x//f/9P/3//r/+v8AAAAA/f8AAPr/AAD6//f/9//6//3/9//3//T/+v/6/wAAAwAAAAAA+v/0//T/8f/x//r/9//6//r/9P/0//r/+v8AAPf//f/6/wAA+v/9//f/+v/9//T/8f/0//T/9//0//T/9//3//H/7f/q//H/9P/3//T/9P/x//H/9P/3//T/9//3//f/9P/x//f/9//6//r/9P/0//T/8f/t/+3/9//x/+3/8f/t/+r/7f/t/+3/8f/0//T/+v/6//f/9P/0//H/7f/t/+r/6v/t//H/9//x//T/9P/n/+r/8f/3//H/9P/0//T/9P/0//H/8f/x//f/9P/3//T/8f/t//f/9//3//H/9//0/+3/9P/0//f/+v/0//H/9P/0//T/8f/x//f/+v/9//f/9P/0//H/9P/3//H/8f/3//f/9P/6//3//f8AAAAA+v/3//f/+v/6/wAAAAAAAP3/+v8AAP3//f/6//3/9//9//r/+v/3//r/+v/0//T/9//3//T/7f/x//T/9P/6//3/AAD9/wAA/f/9//f/9//9//f/9P/3//f/+v/3//3/AAAAAP3/AAD9//3//f8AAP3/+v8AAAAA/f8AAAAAAAAAAAAAAwAGAAAAAAAGAAYACQAGAAMABgADAAAAAAAAAAAAAAAAAAAACQADAAAAAwAAAAAAAwADAAYAAwAAAP3/AAAAAAAAAAADAAMABgAAAAMAAAAAAAAABgAJAAMAAwAAAAMABgADAAYACQAPAAYABgAJAAwACQADAAMACQAGAAYABgAGAAYACQAJAAYAAwADAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAAAAP3/AwAGAAkAAwD9//3/AwADAAMAAwADAAAAAwADAAYADAAGAAMACQADAAAAAAAAAAAAAwADAAAA/f8AAAAABgADAAAABgAGAAYAAAADAP3/AAAAAAAA/f8AAAAAAAD9/wAAAAD9//f/AAAAAAMACQAGAAYAAAAAAP3/+v8AAAAAAAAAAAAAAAAAAAAAAAD9//r/+v/9//3//f/9//3/AAD9//r/+v/6//r//f8AAPr//f8AAPr/+v/6//3//f/9/wAA/f8AAPr//f/9//r/AAD9/wAAAAD6/wAA/f/3//f/9//0//T//f/9//f/+v/9//3//f8DAAAAAAAAAPr//f/9//r/9//9//r//f/6//r//f/6//r//f/9//r/+v/3//f/+v/3//T/+v8AAP3/9//6//r/+v/9//3/AAD6//3/+v8AAAAA/f/6//r/9//0/wAA/f/9//f/9//6//f/9P/6//f/9P/6/wAA9//3//r/+v/3//f//f/0//f/9P/x//f/7f/t//T/8f/t/+3/+v/6//f/+v/0//r/9//0/+r/7f/3//f//f/9//r/9//6//f/+v8AAAAA/f8AAAAA/f/6/wAAAAD6//3//f/9//r/AAD9//r//f/6//r/+v/6//r/+v/9//r//f/6//r/+v/0//3/AAAAAAAAAAAAAAAAAAD9/wAAAAAAAP3/AwAAAP3/AAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAwAAAAAA/f/9/wAA/f/9/wMAAAADAAAAAwAAAAAABgADAAAAAAADAAAAAAD9//3/AAAAAAAAAAAAAAAAAAAAAAMAAAADAAYABgADAAAABgADAAYACQAJAAYABgAGAAYABgAGAAYAAwAGAAkADAAMAAkADAAJAAwADAAMAAwABgAGAAYABgAGAAkADwAMAAkABgAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAAwAAAAAAAAAAAAAA/f8AAAAAAAAAAAMAAAD9/wMACQAGAAAAAAAAAAAAAAAAAAAAAAADAAAA/f/6/wAAAAAAAP3/AAADAAAA/f8AAP3//f/6/wAAAwAAAAAADAADAAAABgD9/wAAAAAAAAAAAAADAAAAAAADAAAAAAD9//3/+v/3//r/+v/3//T/9//3/wAAAAD6/wAAAAAAAAAAAAAAAAAAAAADAAAA+v8AAPr/+v/6/wAAAAD9/wAAAAAAAAAAAAAAAAAAAAAAAAAA/f/9//3/AAAAAAAAAAD3/wAAAAD9/wAAAAAAAPf/+v/0//f/9//6//3/+v/6//f/9P/9//r//f8AAAAAAAAAAAAAAAD6//r/9//9//3//f8DAAMABgAGAAMAAwAAAPr//f8AAAAAAAAAAAAAAAAAAAAAAwADAAYAAAAAAAAAAAADAAMABgADAAAABgADAAAAAwAJAAkACQAMAAkACQAGAAMAAwADAAkABgADAAAAAwADAAYABgAAAAYACQAJAAYAAwADAAMAAwAGAAMAAwAGAAMABgAJAAkABgAJAAkACQAGAAkADAAJAAwACQAJAAwABgADAAYACQAJAAYAAwAGAAYACQAGAAYABgAMAAwADAAPAAkADAAGAAYAAAADAAMAAAAGAAkABgAGAAMACQAJAAwADAAJAAkAAwAAAP3/AAADAAYAAwAJAAkADAAGAAkABgAAAAAAAAAAAAAAAAADAAMAAwAAAAMAAwADAAwADAAJAAYAAwADAAMAAwAJAAMAAwAMAAwAAwAGAAkACQAGAAwADAAGAAYABgAGAAMAAwAGAAMAAwAAAAAAAAADAAAAAAAAAAYAAAADAP3//f/6//3/AAAAAAMAAwAAAAAAAAAAAAAAAAD9/wAA/f8AAAAA/f/9/wAAAwAAAP3/AAAAAAAAAAAAAP3/9//6//r/9//6//r//f/0//f/9//3//T/9P/3//r//f8AAP3//f/6//3//f/9//f/+v/6//r/9//3//T/9//6//3/9//3//T/8f/0//f/+v/6//3/+v/0//H/8f/x//T/9P/0//r//f/6//H/8f/q/+f/9P/3/+3/8f/0//T/9P/0//H/8f/t/+3/8f/0//T/8f/x/+3/8f/0//H/8f/3//r/9P/x//H/7f/q/+f/8f/t/+r/6v/t/+3/7f/q//H/9P/x/+3/7f/x/+r/7f/t//H/7f/t/+3/6v/t//H/8f/3//T/8f/q/+r/7f/n/+f/7f/t/+3/7f/x/+3/5//q/+f/5//q/+r/6v/q//H/7f/q/+3/6v/q//T/9//0//f/9P/q/+f/7f/t/+3/7f/3//H/9P/x/+3/7f/x//T/9P/x//H/7f/0//T/9P/0//3/9P/3//f/9//3//3//f/6/wAA/f8AAAAAAAAAAP3/AAAAAAAAAAAAAAAAAAD6//r/9P/0//T/+v/6//3/AAD6//f/AAAAAPr/AAAAAAAAAAAAAAAA+v/9//f/AAD9/wAAAwAAAAMABgAGAAAA/f8AAPr/AAAAAAYACQAGAAMABgADAAAAAAD0//f//f8AAAAAAwAJAAMABgAGAAkACQADAAMACQAJAAMABgAGAAYACQAJABMADwAMAAkAEwAJAAwADwAPAAwADAAJAAYACQAJAAkACQAGAAkADAAPAAwACQAMAAkADAAJAAkABgAGAAYABgAAAAkABgAMAAYADAAGAAYADAAJAAMACQAJAAkACQAPAA8AEwAPAAwADwAPAA8ABgAGAAMAAAAJAAkABgADAA8ADwAJAAMACQADAAYACQAJAAYABgAJAAMAAwADAAkACQAJAAkADAAJAAMAAAAAAAAABgADAAYACQADAAAAAwAAAAYABgAJAAYACQADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/f/6//3//f8AAAMABgADAAAA/f/9/wAAAAAAAPr/+v8AAP3/AAADAAAAAwADAAAAAwAAAAAA/f8AAPr//f/9//r/+v/3/wAA/f8AAP3//f8AAAAAAAD9//3/AAAAAAAA/f/9/wAAAAAAAAAAAAD9//f/9//0//H/7f/0//f/9//0//r//f8AAPT//f/x//H/9P/0//T/9P/6//f/9//6//f/+v/3//r/9//6//r/9P/0//T/+v/9//r/AAD6/wAAAAAAAP3//f/6//r/AAD9//3//f/9//r//f/3//T/9//3//H/9//3//f/9//3//f/+v/3/wAAAAD6//r//f/9//r//f/6//f/9//3//r/9//6//3//f8AAPr/+v/3//f/+v/9//r/9P/3//T/9//0//T/9//3//3/+v/9//f/+v/0//T//f/6//f/9//6//r/+v8AAP3/+v/6//T/+v/6//3//f8AAP3/+v/9//f/9//3//r/AAD9//r//f/3//3/AAAAAP3/+v/9//T/+v/6/wAA/f/9//3//f/9//3/AAAAAP3//f8AAAAAAAAAAAAAAAAAAP3//f8AAP3/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAPr/AAAAAAAA/f8DAAAA/f8AAAAA+v/9//3/AAAAAAMAAwD9/wAA/f8AAP3/AAADAAMAAwAGAAYABgAJAAYACQAJAAkAAAAJAAkABgAGAAYABgAGAA8ADAAMAAwADAADAAYACQAGAAYABgAGAAMACQAGAAkACQAGAAAAAwADAAYABgAGAAYAAAADAAAAAwAAAAAAAwAGAAMABgADAAkABgAGAAAAAAAGAAMABgADAAYACQAGAAkABgADAAMABgAGAAMAAAAAAAAAAAD6/wAAAAADAAAAAwAAAAAAAAAAAP3//f8DAAAAAAADAAAA/f/9/wAAAAAAAAMAAwADAAYABgADAAMAAAADAAMAAAAAAAAAAAD9/wAAAAAAAAAAAAADAAAAAAAAAAAAAAAAAAMAAwADAAAAAwD9//r/AAAAAAAA/f/9//3/AAAAAAAAAAD9/wAAAwADAAAAAAADAAAA/f/9/wAAAAD9/wAAAAD9/wAA/f/3//r//f8AAPr/AAD9/wAA+v8AAP3/AAAAAP3/AAAAAPr/9//0//f/+v/9/wAAAAD6//T//f/9//3//f/9/wAAAAAAAAMAAAAAAAAABgAAAAAA/f/9//r/+v/6//f/9/8AAAAAAAAAAAAAAAD6//f/+v/9/wAA/f/9/wAAAAAAAAAAAAD9/wAAAAAAAPf/+v/3//T/+v/6//T/9P/6//r/9//9//f/9P/3//H/9//0//f/8f/3//f/9//0//f/9//6//r/+v/9//T/8f/0//T/8f/3//f/9P/3//T/9P/x//T/8f/x/+3/9//0//f/9//t//H/9P/0//T/9//t//H/9P/0//H/8f/x//H/8f/0//H/7f/3//T/9//3//f/9P/q/+r/7f/q/+r/7f/t//H/8f/x//H/7f/q//T/9P/3/+3/8f/t//H/9P/3//r/9P/0/+3/6v/n/+r/8f/t/+r/8f/t/+3/5P/k/+f/6v/t/+3/8f/3/+3/7f/q/+3/7f/t//H/8f/x//H/8f/t//H/+v/6//H/9//0//T/9P/3//r/9//9//r/+v/6//f/9P/0//T/+v/x//T/9P/x/+3/9//6//f/9//6//r/9//3//f/9//3//T/9//3//3//f/6//T/+v/6//r/9//0//f/+v/3//f/9P/0//T/9P/6//r/+v/9//r//f/9/wMAAwADAAAAAAAAAP3/AAD9//3/+v/3//3/+v/9/wAAAAAAAAAAAAADAAAAAAAAAAAA/f8AAAAAAAADAAYACQAJAAYACQADAAYAAwAGAAMAAwAAAAYAAwADAAMAAwAAAAAAAAAGAAMAAAAGAAMAAAAGAAYAAwADAAMAAwADAAAAAAADAAYAAAAAAAAABgAAAAMABgADAAYABgADAAMAAwAGAAkADwAJAAkADAAMAAwABgAMAA8ADwAPABMAFgATABYAEwAPAAkACQAJABMAEwAPAA8ADAAPAAYADAAJAAwAEwAWAA8ADAAMAAkADwATABkAFgATAAwAEwATAAwADwAWABYAFgATABMADwAPAAwADAAMAAwAEwAMAA8ADwATABMAEwATAA8ACQAJAAkADwAGAAwADwAMAA8AEwAZABMADwAPABYADwATAAwAEwAMAAkADAADAAYACQAJAAwADwAPAAkACQAMAAYABgAJAAwACQAJAAkACQAMAAYACQAJAA8ADwAJAA8ADAAPAAwACQAAAAMACQAPAAYADAAMAAwADAAPAAkADAAJAAkACQAGAAMABgAGAAkABgAJAAwABgAJAAwADAAJAAYAAwADAAYAAwADAAAACQAMAA8ABgAJAAkACQAGAAMAAwADAAMAAwAAAAAABgAAAAYAAwAAAP3/AwAAAAYABgAAAAAAAAADAAAAAwADAAMACQAJAAYAAwADAAAABgAAAAAA/f8DAAMAAAAAAAMAAwADAAMACQAGAAYAAAD6//3/AAAAAAAAAAAGAP3/AAAAAAAA/f8AAAMAAwADAAAA/f/6//r/+v/3//r/+v8AAAAAAAADAAAABgAAAAAAAAD9/wAA/f/9//f//f/9/wAAAAAAAAAA+v/0//f/9P/0//3//f/9/wMAAAD9/wMAAwADAP3/AAAAAAMAAAAAAPf//f8AAP3/+v/6//3//f/6//3//f/9//f/9//3//3//f/9/wAA/f/6//r/+v/6//f//f/6//r/+v/3//3/+v8AAP3//f/9//r/+v/0//f//f/3//r//f/6//H/9//6//f/9P/x//H/9//6//r//f/6//r/+v/3//H/8f/3//f//f/6//f/+v/t/+3/9P/3//T/9//9//r/9//3//f/9P/0//r/+v/6//f/9P/x//3//f/6//T/9//3//H/9P/3//f/9//6//H/9P/3//f/9P/3//r/+v/9//f/9//3//f//f/9//r/9//6//r/9//6//r/+v/9//3//f/6/wAAAAAAAAMAAAAAAPr/+v8AAP3//f8AAAAA/f/9//r/+v/3//r/AAD3//f//f/6//3/8f/3//f/9//9//3/AAAAAAMAAAAAAP3/9//9//f/+v/9//3/AAD6/wAABgADAAMAAwD9/wAAAAAAAAAAAAAGAAYAAwAAAAMAAAAAAAAAAwAGAAAAAAAJAAwACQAJAAYACQAGAAYABgADAAkAAwD9/wAABgAAAAAABgAJAAYABgAGAAkABgADAAAAAAAAAAAAAAADAAMABgADAAkABgADAAMACQAJAAYACQAGAAkADAAJAAYABgAGAAMABgAGAAkADAAGAAYABgADAAMAAwAAAAAABgAGAAYABgAAAAMABgAGAAMABgADAAAAAAD9//r//f/9//r/BgAGAAkAAwAAAPr/AAAAAAAAAAAAAAAAAAAAAAYABgAAAAMABgAGAAAAAAAAAAAAAAAAAAAA+v8AAAAAAwADAAAAAAAAAAYAAwADAAAA/f/9//f/9P/3/wAAAAD6//3/+v/9//T/AAD6//3/AwAAAP3//f/6//r/+v8AAP3/+v8AAP3/AAAAAAAAAAAAAPr/9//6//r/+v/3//r//f/6//f/+v/6//r//f8AAP3//f8AAAAAAAD9/wAAAAAAAAAAAAAAAP3//f/6//f/+v/9//3//f/3//3/9//3//r/+v/3//f//f/9//T/9//6//3//f8AAAAAAAAAAP3//f/9//3//f8AAAAAAAAAAAAAAAAAAAAAAwAAAP3/+v/3//f/+v/6//r//f8AAP3//f/9//r/+v8AAAAAAAAAAAAAAAADAAAAAAD9/wAA+v/9/wYAAAAAAPr/AAAAAAAA/f8AAP3/+v8AAAMAAAAAAP3/+v/3//f/+v/0//f/9//3//3//f8AAAAA/f/6//f//f/9//r//f/3//r/9//3//H/9P8AAP3/AAD9//3/+v/9//3/AAAAAAMAAAAAAP3/+v/3/wAAAAD9//3/AAD9//r/AAD9/wAAAAD9//3//f8AAP3//f8AAP3//f/9//3//f/6/wAA/f8AAAAAAwADAAMAAAAAAAMAAwAAAAAAAwAAAAAAAAAAAAAAAAADAAYAAwAAAAAAAAAAAAAAAwAAAAMAAwAAAAAAAAAAAAAAAAAAAAYAAwAAAAAAAAD9/wAAAwAAAAAAAwADAAAAAAAAAAMAAAAGAAMAAwAAAAYACQADAAMAAAAAAAAAAwAAAAAABgADAAMABgADAAMABgAJAAYAAwADAAYABgADAAYABgAGAAMACQAGAAkABgADAAYAAAADAAAA/f8AAAAAAwADAAMAAAAAAAAAAwADAAAAAAAAAAAABgADAAMAAwAAAAAAAAADAAYAAAAAAAAAAAD9/wMABgD9/wAAAwAAAAAAAwAAAAAAAwADAAAA/f8AAP3/AAD9/wAAAwAAAAAABgAAAAAA+v/6//T/9//3/wAAAAAGAAYAAwAAAAAAAAD6/wAAAAAAAAAA/f8DAAAAAAAAAAAAAAD9//3/AAD6//3/+v/3//T/9//3/wAAAAD6//r/AAAAAP3//f/6//3/AAADAAAA/f8AAPr//f/6/wAAAwD9/wAAAAAAAAAAAAAAAP3/+v/3//3/AAAAAAAAAAAAAAAAAAD6/wAAAwAAAAAAAAAAAPr/+v/3//3//f/6//r/+v/6//r/9//9//r//f/9//3//f8AAAAAAAD6//3//f/9//3//f8AAAMAAwAAAAAAAwADAAAAAAAAAP3//f8AAAAAAAD9/wAAAAAAAAYAAAAAAAAAAAAAAAAAAAD6//r/AAD9//3//f/9/wAAAwAGAAAAAwADAAAAAAAAAAMAAwADAP3/AAAAAAAAAAD6/wAAAAAAAAAAAwAAAAAAAAADAAYAAwADAAAAAwAAAAMAAwAGAAYABgADAAMABgAGAAYAAwADAAYAAwAAAAYABgAGAAMAAAAAAAAAAAAAAAAAAAAGAAMAAAAAAAAABgAAAAYAAwADAAYAAAADAAMAAAAAAAAAAAAAAAMABgADAAYABgADAAAAAwADAAYAAwAGAAMACQAGAAYACQAAAAAAAAADAAMAAAAAAAAAAAD9/wAAAAAAAAMABgAGAAMABgADAAMAAwADAAMAAAAJAAkAAwADAAYABgADAAYABgADAAAAAAAAAAAAAAAAAAMAAAD9/wAA/f8AAP3/+v8AAAAAAAAGAAMAAAAAAAAAAAD9/wAAAAD9/wAAAwAAAAAAAAAAAAMAAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAADAAAAAAAAAAAAAAD0//r/+v/6//f/+v/9//3/AAD9/wAA/f/9/wAAAAAAAP3//f/9//f/9//9/wAAAAAAAAAA+v/9//r/+v/3//r//f/9//3//f/3//T/9//3//r/+v/3//r//f8AAPf/9//x/+3/9//3//f/9//3//f/+v/6//f/+v/6//f/+v/6//3/9//0//T/9//6//r/+v/6//3/+v/3//f/+v/3//f/+v/6//f/9//9//3/9//3//3/AAD9//f//f/6//r/+v/6//r/9//3//f/9//6//r/+v/9/wAA/f/3//f/+v/3//f/9//6//r/+v/6//f/9P/3//T/9//6//r/+v/6//r/9//3//r/8f/0//f//f/9/wAAAAD3//r//f/9//r/+v8AAP3//f8AAP3//f/9/wAAAAD9//3/+v/9/wAAAAD6/wAA/f8AAAAAAAAAAAAAAAAAAAMAAAAAAAMAAAAGAAMABgADAAYACQADAAYAAwAAAAAA/f/6//r//f/9/wAA/f/3//f/AAAAAAAAAwAAAP3//f8AAAAAAAAAAP3/AAAAAAAABgD9/wAAAAADAAAA/f8AAAAA/f/9/wAAAwAAAAAAAAAAAP3/+v/0//T//f/9//r//f8AAP3/AAAAAAMABgAAAP3/AwAAAAAAAAADAAYACQAGAAwACQAGAAYADAAGAAkABgADAAAAAwAAAAAAAwADAAYAAwAAAAMABgAGAAYAAAADAAMAAwAAAAAAAAAAAAMAAwAAAAMAAwADAAAAAwD9//r/AAAAAAAAAAAGAAAAAAAGAAMAAwAAAAAAAwADAAkABgAAAAAAAAADAAYAAwD9/wMAAAAAAP3/AAAAAAAAAwADAAMAAwADAAAAAAAAAAMABgAGAAAAAwAAAAAAAAAAAAAAAwAAAAMABgADAAAAAAAAAAAAAwAGAAAAAAAAAAAA/f/9/wAAAwADAAMAAwADAAAAAAAAAAAA/f/9/wAA/f/9/wAA/f8DAAMAAwAAAP3//f/6//3//f/9//3/+v/9//f/9//9//3/AAAAAAAABgADAAYAAAADAAAAAwAAAAAA/f/9/wAA/f8AAP3/AAADAAYAAwAAAP3/AAADAAMAAAAAAAMAAAAAAAAAAAD9//3//f/6//f/+v8AAAMAAAD9/wAAAAAAAPf//f/0//T/9P/6//3/+v/9//3/AAAAAAAAAwAAAAAA/f/9/wAA/f/9/wAAAAAAAAAAAAD9/wAAAAADAAAAAAD6//f/AAD9//r/AAAAAAAACQADAAAAAAAAAPr/AAAAAAAAAAAAAAAAAAAAAAYAAwAAAAAAAAAAAAAAAAAAAP3/AAD9/wAAAAADAAMAAwADAAAAAAD6//3/AAAAAAAA+v/6//r//f/9//r//f/9//3//f/9//r//f/3//3/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAMAAAAAAAAA+v/9//f/9//3//f//f/9//3/AAAAAAAAAAAAAAAAAAAAAPr//f/9/wAAAAAAAAAAAAD9/wAAAwADAAAAAAAAAAAAAAD9/wAAAAAAAAAA+v/9//T//f8AAAAAAAAAAAYAAwADAAkAAwAAAAAAAwAGAAAAAAAAAP3/+v/9/wAAAAAAAAAAAAAAAAAAAAD6//3//f8AAAAAAAAJAAYAAAAGAAYAAwADAAMAAwAAAAMAAAAJAAkABgAAAAAAAAADAAkACQAJAAwACQAAAAAAAwAAAAMABgADAAYACQADAAYABgADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAAAAAAAAMAAwAGAAMAAAADAAAAAAD9/wAAAwAAAAMAAwAAAAMAAwAAAAAA+v/6//3/+v/0//3//f8AAAAAAwAAAAMAAAAAAAAAAAAJAAMAAAAAAAAA/f8AAAAAAAD9/wAAAAAAAAMAAAAAAAMAAAADAAYAAwAAAAAAAAD9//3/AAAAAAAAAAAAAP3//f/9/wAAAAAAAAMAAAAAAAAAAAD3//T//f/9/wAA/f/3//3/AAAAAP3//f/6/wAAAAADAAAAAAAAAAAA+v/6/wAAAAD9//f/AAAAAAAAAAD3//r/+v/9//f//f/9/wAA9/8AAAAAAAAAAP3/AAAAAP3/9//3//f/+v/9/wAAAAAAAPr/AAAAAAAAAAD6//3/AAD9/wAAAAD9/wAAAwAAAAAAAAAAAPr//f/9//r/9//9//r/9//3//f/+v/9/wAAAAADAAMAAAD9//r//f8AAAAAAAAAAAYAAwAAAP3//f/9//r//f8AAP3/+v8AAAAA+v/9//r/+v8AAP3/AAD9/wAA+v/3//f//f/3/wAAAAAAAP3//f/6//r//f/6/wAA+v/9//3/9/8AAPr//f/9/wAA/f/9//r//f/3//3/+v/x//T/+v/6/wAA/f/3//f/+v/6//r/+v/6//r/+v/6//3/+v8AAPr/+v/6//r/+v/0//f/9//3//3//f/6//3/+v/9//r/+v/0//3//f8AAP3//f/9//3/AAD9/wAA+v/6//T/9//0//r/AAD9//f//f/6//3/9P/0//3/+v/3//3/+v/9//H/9//3//r/9//3//3/+v/6/wAAAAD9//r/AAAAAP3/AAAAAAAAAAAAAAAA/f8AAP3//f8AAAAA/f/6//3/AAD9/wAAAAD6//T//f/6//3/+v/9/wAA/f8AAAAAAAAAAPr//f/9/wAAAAD9//3/AAAAAAMAAAD9//3/AAD9/wAA/f/6//3//f8AAAAAAAAAAAAAAAAAAAMAAAAAAP3/AAAAAAAAAAAAAAMAAAAAAAYAAAAAAAYAAAAAAAAAAAADAAAAAAAAAAYAAAAAAAAABgAJAAkACQAGAAkACQAGAAYAAwAMAAYAAwAAAAAAAAAAAAAAAAADAAYAAAAGAAMAAAADAAMAAAADAAMAAAAAAAAAAwAGAAAAAwAAAAMAAAAAAAAABgAAAAMACQAGAAYAAwADAAMAAwAAAAYADAAGAAYAAwAGAAYAAwAJAAkACQAJAAkACQAGAAwADAAJAAYABgAGAAwADAAGAAwACQAMAAYADAAJAAkACQAMAAkAAwAAAAAABgAMAA8ADwAMAAwADwAMAAYACQAJAAkACQAMAA8ACQAJAAYABgAGAAYABgAAAAYABgAJAAwADAAMAAkABgADAAMABgAAAAYACQADAAMAAAADAAAAAAADAAkAAwAGAAMACQADAAMABgAAAAAAAAADAAYABgAGAAAAAwADAAAAAAD9/wAAAwAAAAAAAAADAAAAAAAAAAkABgAAAAYAAwADAAAAAAAAAAAABgAJAAMAAwADAAAAAAADAAMABgADAAYAAwAAAAAAAAAAAAAAAAAAAAMAAAADAAMABgADAAAAAAAAAAMAAAAAAAAAAAAGAAYAAAADAAAAAAD9//3/AAAAAAMAAAD6//r/AAAAAAMAAAAAAPf//f/9/wAAAAD6//r/AAAAAAAAAAAAAAAAAwADAAMAAAAAAAMABgADAAAAAAAGAAAAAAD6/wMAAAAAAAYACQAGAAYAAAD9//3/AAAAAAAAAAADAAAAAAADAAMAAAAAAAAAAAD9/wAA/f/6//3/AAD9/wAAAAAAAAAAAAAGAAYACQAAAAAA/f/6/wAA+v/9//r/AAAAAAYABgAGAAkAAwAAAAMAAAAAAAYAAAAAAAMAAAAAAAMAAAADAAAAAAAAAAMAAwAAAAAAAwAJAAYAAAAAAAAAAwAAAAAAAAAAAAAA/f/6/wAAAAD9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAMAAAAAAAAAAwAAAAAAAwAAAP3//f/9//f//f/9/wAA/f/9//3/AAAAAAAAAAD9//r/+v/6//3/+v/3//3/AwD9/wAAAAD3//3//f8AAP3//f/9//3/9//6//r/+v/6//3/AAAAAAAA/f/6/wAAAwADAAAAAAAAAPr/+v/9/wAAAAD9//f//f/6//3//f/3/wAA/f8AAP3//f8AAAAAAAAAAP3//f/9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMABgAGAAAAAwADAAAAAAAAAAMAAAAAAAAAAAD9/wAAAwD9//3/AAD9//3/9/8AAP3//f8AAAMAAwADAAYAAAADAAAAAAADAP3//f/9/wAAAAD9//3/AAAAAAAAAAD9/wAAAAAAAAAAAAAGAAMAAAAAAAMAAAAAAAAAAwADAAAAAAAGAAkAAwAGAAYACQAJAAMABgAAAAMAAAD9/wAABgADAAMABgAJAAMAAwAAAAYAAwADAAAAAwAAAAYABgADAAYAAwAAAAYABgAAAAAAAwAGAAAAAwAAAAMABgAGAAYAAwAJAAMAAwAGAAkACQAGAAMABgADAAYACQAAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAP3/AAAAAAAA/f/9//f/AAD9/wAAAAD9//3/BgAGAAYABgAAAP3//f8AAAMACQADAAAAAwAAAAAAAwAAAAAAAAAAAAAA/f8DAAAAAAAAAAAAAAAAAAMAAwAGAAAAAAAAAAAA+v8AAAAAAAD9/wAA/f/6//f/+v/6/wAABgADAAMAAAD9//r/+v/9/wAA/f8AAP3/AAAAAAAAAwADAAAA/f8AAP3//f/6//3/AAAAAAAAAAAAAAAAAAAAAP3/+v/9//r//f/9/wAAAwAAAAMAAAAAAAAA/f/9//f//f8AAAAAAAD9/wAA/f/6//r//f/3//r/AAAAAAAA/f8AAAAAAAADAAAAAAAAAP3//f8AAAMAAAADAAMAAAAAAAAAAAAAAAAAAwAAAAAAAAD6//3//f/9//3/AAADAAMAAAADAAAAAAADAAAAAAAAAAMAAAADAAYAAwAAAAAAAAAAAAkAAwADAAAAAAADAAMA/f8AAAAA/f8AAAAAAAAAAAAAAAD9//3/AAD6/wAA/f/6/wAA+v/6//r//f/6//f/AwADAAAAAAD6/wAAAAD9//f/9P/9//3/AAAAAAAA+v/9//3/AAAAAAMAAAADAAAA/f/6/wAAAwAAAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAAAAAAAAAADAAMAAwAAAAAA/f/6/wAAAAAAAAAAAwAGAAkABgADAAMABgAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAMABgAAAAAAAAAAAAYABgADAAAAAAAAAAAA/f8AAAMAAAAAAP3//f/9//3/AAAAAAAAAAAAAAAA/f/9/wAAAAAAAAAAAAAAAAAAAAAAAAYAAwAGAAMAAAAAAP3/AAD9/wAAAwADAAAABgAGAAAAAAAAAAAAAAAAAAMABgAJAAMABgADAAYAAwADAAMAAAAAAAAAAAAAAAAAAwADAAAAAAAAAAAAAAADAAAA/f/9//3/AwAAAAAAAAD9//r/AAAAAAMAAAAAAPr/+v/9/wAAAAD9/wYABgADAAAAAAAAAP3//f8AAAAAAAAAAAAA/f/6/wAA/f/9//r/AAAAAAAA+v/6//f/+v/3/wAAAAAAAAAAAwAAAAAAAAD9/wAAAAAAAAAA+v8AAAAAAAAAAAAA/f/9//3//f/6//3//f/6//f//f/6/wAAAAD6//r/AAAAAP3//f/6//3/AAAAAAAAAAADAP3/AAD9/wAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAwAAAAMAAwADAAAAAAD6/wAAAAAAAAMAAAADAP3//f/6//r/AAD9/wAA/f/9//r/9//9//3//f/9/wAAAAAAAAAAAAD9//3//f/9/wAAAAADAAMAAAADAAMAAAAAAP3/AAAAAP3/AAAAAP3/AAAAAAAAAwAGAAkAAAAAAAAAAAAAAAAAAAD6//r/AwAAAP3/AAAAAAAAAAAGAAAAAAAAAP3/+v/9/wAAAAADAAAAAAAAAAMAAwAAAAAAAwAAAAAAAwAAAAAA/f8AAAAAAAADAAAAAwADAAYABgAGAAMABgAAAAYABgAAAAAAAAAAAAMAAAAAAAMABgADAAAAAAAAAAAAAAAAAAAAAAADAAYABgADAAMABgADAAkACQAJAAYAAAADAAMAAAAAAAAAAAAAAAMABgAGAAkABgAGAAAAAAAAAAMAAAAAAAAABgAAAAYACQAGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAYAAAADAAAAAwAGAAMABgAGAAMAAAADAAkAAwAGAAYACQAJAAwADAAJAAkABgADAAAAAwAAAAMAAwAAAAAAAAADAAMAAwAGAAYAAwAGAAAAAAAAAAAAAwAGAAkACQADAAAAAAAAAAMABgADAAkABgAGAAYAAwADAAMAAwADAAAAAwADAAMABgADAAAAAAAGAAAAAAAAAAAAAAD6/wAAAAD6//r/+v8AAP3/AAAAAAMAAAAAAAMAAwADAAAAAAAAAPr/+v/6//3/AAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAAAD6//f/9//3//r//f/6/wAAAAAAAAAAAAD3//T//f8AAAAAAAAAAAAAAAAAAP3/+v/3//f//f8AAAAAAAD9//3//f8AAAAAAAAAAAAA/f/9//r/+v/6//3/AAAAAAAAAAADAAYAAAD9/wAAAAD9//r//f/9/wAAAAAAAAAAAAAAAAAA/f/9//r/AAAAAAMAAwD9/wAAAAD9//3//f8AAPr/AAAAAP3/+v/9//3//f/9/wAAAAAAAAAAAAD9/wAA/f/6//3/AAAAAAMAAwAAAAAAAAAAAP3/+v/9//r//f8AAAAAAAAAAAAABgAAAAAAAAD9//3/+v/9/wAA/f8AAAAA/f/9/wAAAAAAAAAAAAAAAAMAAwAGAAAAAAADAAkACQADAAkAAwAAAAAA+v/6//r//f8AAAAAAAD9//3/AAAAAAAAAAAAAAAA/f/9/wAAAAADAAMABgAAAAMAAwD9/wAAAAAAAP3/+v/9//3//f/9/wAAAwAAAAAAAAAAAAAA/f/3//r/+v/6//3/+v/9//3/AAAAAAAAAAD9//3/AAAAAAAAAwAAAAAAAwAAAAMAAwAAAAAABgAAAAMABgADAAAAAwADAAAA/f8AAAAAAAAAAAMABgADAAAAAAADAAAAAAAAAAAA/f/9//3/AAD9/wAAAAADAAAAAAD9/wAAAwADAAAAAAAAAP3//f8AAAAAAwAAAP3/AAAAAAMAAwAAAAAAAAAAAAAAAAD9/wMAAAAAAP3/AAAAAAAAAAAAAAAAAAADAAAAAAD9/wAAAwAAAAAAAwADAAAAAAAAAAAAAAADAAAAAAADAAMABgADAAMAAwADAAAAAAAAAAAA/f8AAAAAAAAAAAMAAwAAAAAAAwAAAAMAAAAAAAAAAAAAAAMAAAAAAAAAAwAAAAAAAAD9/wAAAAAAAP3/AAADAAAAAAADAAAAAAAAAAAAAAAAAAAA/f8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAwAAAAAAAAADAAMAAwADAAYAAwADAAAAAAD9/wAAAAD9/wAAAAAAAAYAAwAAAAYAAwADAP3/AAD6//r//f8AAAMAAwADAAAAAAAAAAAAAAD9/wAAAAAAAAAAAAD9//3/AAAAAAMABgAAAAMAAwAGAAMAAwAAAP3/AAD9//3/AAD9//r/AwD9/wAAAAD9//3/AAAAAAAAAAAAAAAAAAAAAAMAAwAAAAAAAAAAAPr/AAAAAP3/AAAAAAAA/f8AAAAAAAAAAP3/AAD9//3//f8AAAAA+v/6//f/9//6//r//f/9/wAA/f/6//f//f/0//f/AAAAAP3/+v/9/wAAAAAAAP3//f/9//f/AAD9//3/AAAAAAAA/f/9//3/+v/3//r/AAD9//f/+v/3//r/+v/9/wAAAAAGAAAAAAD6//3//f8AAAAAAAAAAP3//f/6//r/AAAAAAAAAAAAAAAAAAD9//3/9//3//T//f8AAP3//f8AAAAAAAAAAAAAAAAAAP3//f/9//3/9//6//3//f/9/wAA/f/9/wAAAwADAAAAAAD6//r/+v/9//r//f8AAAAA/f8AAAAAAAAGAAMAAwADAAYA/f8DAAMAAAAAAAAAAAD9/wYAAwAAAAAAAAAAAAAAAAADAAMAAAAAAAAA/f/9//3/AAD9//3/AwAAAAAAAwD9/wAAAAAAAAAAAAAAAAAAAwADAP3/AAAAAAMA/f8AAP3//f8DAAAAAAD9//3//f/9//3/AAAAAAAAAAAAAAAA/f/6/wAAAAD9/wAA/f8AAP3/AwAAAAAAAAD9//3/+v8AAAAA/f8AAAAAAAAAAAAAAAD6/wAAAAAAAAYABgAGAAkABgADAAMAAAAAAP3/AAAAAAAAAwAGAAAAAAADAAMAAwAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/f/6//3//f/9//3/AAAAAAAAAwADAAAAAAAAAAAA+v/6/wAA/f8AAAAABgAAAAAAAAD9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAA/f8AAAAAAAAAAAMAAwAAAP3/AAAAAAAAAAAAAAMAAwADAAMAAAAAAAAAAwAAAAMAAwAAAAAAAAAAAP3/+v8AAP3/AAAAAAAAAAD9/wAAAAADAAMAAAAAAP3//f8AAAMAAwD9/wMAAwAAAP3//f/9//r//f/9//r/+v8AAP3/+v/6//3//f/9//3/AAAAAAAA+v/3//T/9P/x//f//f/9/wAAAwAAAP3/AAD9//3/AAAAAAAA+v8AAPr//f/9//3//f/9//f/+v/3//r/+v/0//T/+v/3//3//f/3//r/AAAAAPr/+v/0//f/+v8AAP3/+v8AAPf/+v/6/wAAAAD6//3//f/3//r/+v/6//3/+v/6/wAAAAD9/wAAAAAAAP3//f/3//r/+v/6//3//f8AAPr//f/6//r//f/6//f/9P/x//f/9P/3//3//f8AAP3//f8AAPr/+v/6//3//f/6//3/+v8AAAAAAAAAAAAAAAAAAP3/AAAAAP3/+v/9//3/AAAAAAAAAAADAAMAAAAAAAAAAAAAAAAAAAD0//T/AAD9//3/AAD9/wAAAAAAAAAAAAAAAAAAAAD9/wAAAAAAAPr/AAAAAAAA/f/6//3/AAAAAAAAAAD6//3//f8AAAAAAAAAAAAAAAAAAAMAAwAGAAMAAAD9/wAAAAAAAAAABgAGAAkABgADAAYAAwADAAAAAAAAAAAAAAAAAAMAAAADAAAAAwAGAAMABgAGAAMAAwAAAAMAAAAGAAkAAwADAAMAAAADAAMAAwADAAYAAAAAAAAAAAADAAMAAAADAAMABgAGAAMAAAAAAAAAAAADAAMAAAAAAAAAAwAAAAMAAwAAAAMAAwAAAAAAAAAAAAMABgAGAAYABgAMAAkAAAADAAMABgAGAAYACQAGAAYABgAGAAYABgAGAAwACQAAAAMABgAJAAMABgAGAAYAAwAGAAMAAAAAAAAAAwAGAAkACQADAAYABgAGAAkACQAMAAwACQAGAAYAAAAAAAAAAAADAAMADAAJAAwADAAJAAYABgAJAAYAAwAAAAAAAAD6/wAAAAAAAAMAAAAAAAAAAAAAAAMAAAADAAAABgADAAAAAwAAAAAAAAAAAAYAAwADAAAAAAAAAAAAAAAAAAAAAwAGAAAAAAAAAP3//f8AAAAAAAAAAAAAAAAAAAAAAAD6//r/AAAAAPr//f8AAP3/AAAAAAAAAwADAAAAAwAAAAAAAAD9//3//f8AAAAAAAAAAAAAAAAAAP3//f8AAAAAAAAAAAAAAAADAAYAAAAAAAAAAAD9//3//f8AAAAAAAAAAAAAAAD9/wAA/f/6//f//f8AAAMAAwD9//3/AAD9//3//f8AAP3/AAAAAAAA/f/9/wAAAAAAAAAA/f8DAP3//f/6/wAA+v/6/wAABgADAAAAAAD6//r/AAD9/wAAAAAAAP3/AAAAAAAAAAD9//3/AAAAAAAA/f/9//3/+v/6//3/+v/9//3//f8AAAAAAwADAAAAAAAAAAAAAAAAAP3//f8AAAMAAwAAAAAAAAD6//3//f/6//3//f/9/wAA/f/9/wAAAwADAAAAAwAAAAAAAAAAAAAAAAAAAAAA/f/9/wAAAAD9/wAAAwADAAAA/f/6//3/+v/9/wAAAAAAAAAA/f/9//r/+v/3//f/+v/3//r//f8AAAMAAwAAAAMAAwD9//3/AAD9//3/AAAAAP3/AAAAAAAAAAAAAAAAAAD9/wAAAAAAAP3/AAAAAAAA/f8AAAAAAwAAAAAAAwD9/wAAAAAAAAAAAAAAAAAA/f/9//r//f/6/wAAAAAGAAAAAAD9/wAAAAAAAPr//f/9//3//f8AAAAAAAAAAP3//f8AAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAAAAPr//f8AAAAAAAAAAAAAAAAAAAAAAAAAAAMABgADAAMAAwAAAAAAAAAAAAAAAAAAAAMAAwADAAMABgAAAAMAAwAAAAAAAAAAAAAA+v8AAP3/AAAAAAMAAwAAAAAAAwADAAAAAAAAAP3/AAAAAAYAAwADAAMABgAAAAAA/f/3//r/+v/6//r//f8AAAAAAAAAAAMAAAADAAMABgADAAAAAAADAAAAAAAAAAAAAAADAAYAAwADAAYABgAAAAMABgAAAAAAAwAGAAMAAwADAAkABgADAAAAAwAAAAMAAwAAAAMAAAAAAAYAAwAAAAMABgADAAAAAAD9/wAAAAAAAAAAAwAGAAAAAAAAAAMACQADAAMABgADAAYAAwAAAAAAAAAAAAAAAwAAAAMABgAJAAMAAwAAAPf//f/9/wAAAAAAAPr/AAD9/wAAAAD6//3/BgAAAAAAAAAAAAAAAAAAAAYABgADAAAAAAAAAPr//f/9//3//f8AAAAAAAAAAAAAAAAAAP3/AAAAAAAAAAAAAAAA+v/3//r/9//6//3/AAD9/wAA/f8AAPr/AAD6//r/AAD9/wAAAAD9//r/AAAAAP3//f/9//3/AAAAAAAAAAAAAP3/9//6//r/9//3//r/AAD6//f/+v/6//r/AAAAAAAAAAADAAAAAAD6/wAAAAAAAAAAAAAAAAAAAAAAAP3/AAAAAAAAAAAAAP3//f/6//r/+v/0//T/+v/6//r/+v8AAAAAAAAAAAAAAAADAP3/AAAAAAAA+v/9//3//f/9/wAAAAAAAAAAAwAAAAAA+v/6//f/9//3//f//f8AAAAA/f8AAP3/AAADAAAAAwAAAAMAAAAAAAAAAAD9/wAAAAAAAAkAAwAAAAAAAAAAAAAA/f8AAAAAAAAAAAAAAAAAAAAAAAAAAP3/AAD9/wAAAAD9/wAA/f/9/wAA/f/6//r/AAAAAP3/AAAAAAAAAAAAAP3/9//9//3//f/6//f/+v/6//r/AAADAAMAAAAAAAAA+v/3/wAAAAAAAAAAAAAAAP3/AAD6//r/AAD9/wAAAAADAAMAAAAAAP3/+v/6//f/+v/9/wAAAAAAAAMAAAAAAAYAAwAAAAMAAwADAAMAAAAAAP3/AAAAAP3/AAADAAAAAwADAAYAAwAAAAAAAwAAAAMAAwAAAP3//f/9/wAAAAAAAAAAAAAAAP3/AAD9/wAAAAAAAP3/AAAAAAAA/f8AAAAAAAAAAP3/AAAAAAMABgAAAAAAAAADAAMAAAAAAAAAAAAAAAMAAwAAAAAABgAGAAAA/f/6//3/+v/9/wMABgAGAAAACQAGAAYAAwAAAAMAAAAAAAAAAAAAAAAAAwADAAAA/f8AAAAAAAAAAP3/+v/9//3/AAAAAAAAAAD9//3/AAAAAAMAAAAAAPr//f8AAAMAAwAAAAMAAwAAAAAAAAD9//3/AAAAAAAA/f8AAAAAAAD9/wAAAAD9//r/AAAAAAMAAAAAAP3/AAD9/wAAAAAAAAAAAAD6//r//f/6/wAAAAAAAAAAAAADAAAA/f/9/wAAAAAAAAAAAAD9//3//f/3//T/9//3/wAAAAD3//r/AAAAAPr/9//3//r//f8AAAAA/f8AAPr/+v/3//3/AAD9/wAAAAAAAAAAAAD9//3/+v/6/wAAAAAAAAAAAAAAAAAAAAD9/wAAAAAAAAAA/f8AAPr//f/3//r//f/9//3//f/6//r/+v/9//3//f/9/wAAAAAAAAAA/f/6//r/+v/9/wAAAAAAAAAAAAADAAMAAwADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAADAAYAAAD9//3/AAAAAAAAAAD9//r/AAD9//3//f8AAAAAAwADAAAAAAAAAAAA/f/9/wAAAAAAAP3/AAAAAAMAAwAAAAAAAwAAAAMAAAAAAAMAAAAAAAAAAAAAAPr/AAD9/wAAAAADAAAAAAD9/wYACQADAAYAAwAAAAMAAAAAAAMAAwADAAMAAAAAAAAAAAAAAAAAAAAGAAMABgADAAMABgADAAYAAwADAAMAAAADAAMAAAAAAAMAAAAAAAYABgAGAAkAAwADAP3/AAAAAAAAAAADAAAAAwADAAkABgAAAAAAAAADAAAAAAAAAAAAAAAAAAAAAwAAAAMABgADAAMAAwADAAMABgAGAAYAAAAJAAYAAwADAAYABgAGAAYAAwAAAAMAAwAAAAAAAwAAAAYABgAAAAAAAAADAAAAAAADAAMAAwADAAAAAAAAAAAAAwAAAAMAAwAAAAAAAwAGAAYABgAJAAwAAwAAAAMAAwAAAAMABgAGAAAAAwADAAMAAAAAAAMAAAADAAAAAAAAAAAAAAD6//3/AAADAAAAAAAAAAAAAwADAAMAAAAAAAAAAwAAAAAAAwADAAAA/f8AAAAAAAAAAAAAAAD9/wAAAAD6//3/AAAAAAAAAAAAAAAAAAD9//3/AAD6//3/AAAAAP3//f/6//f/AAAAAP3/AAAAAAAAAAAAAAAAAAAAAAAAAAD9/wAA+v/3//r//f8AAP3/AAAAAAAAAAD9/wAA/f8AAAAAAAADAAAAAAAAAAAA/f/6/wAAAAD9//3/AAD9/wAAAAD9/wAAAAAAAAMAAwADAAAAAAADAAYAAwD9//3/AAD9//3//f/9//3/AAAAAAAA/f/6//r//f8AAP3//f8AAAAA/f/9/wAA/f/6/wAAAwAAAAYAAAD9//3/AAAAAAAA/f8AAPr/+v8AAP3/AAAAAAAAAwAAAAMAAAAAAAAAAAD9//3//f/9/wAAAAAAAAAAAAAAAAMAAwADAAYAAwAGAAAAAAAAAAMAAwADAAMAAAAAAAMAAAD9/wAAAAAAAAAA/f/9//3/AAAAAAMAAwAAAAAAAAAAAAAAAAADAAAA/f/9/wAAAAAAAAAAAwAGAAAAAAAAAAAAAAD9/wAAAAAAAPr//f/9//r//f/6//3//f8AAAAAAAAAAAAAAAD9/wAAAAD9//3/AAAAAAAAAAAAAAAAAAADAAMAAAAAAAAAAAAAAAAAAAAAAAAAAwADAAMAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAA/f/9//3/AAD9/wAAAAAGAAAAAAAAAAAAAwAAAAAAAAAAAAAA/f8AAAAAAAADAAAAAAAAAAMAAwAAAAAAAAAAAP3//f/6/wAAAAD9//3//f8AAAAAAAAAAAAAAwAGAAMAAwAAAAMABgADAAMAAwAAAAAAAAAAAAAAAwADAAkAAwADAAAAAwAAAAMABgADAAAAAAAAAAAAAAAAAAAAAAADAAYABgADAAMAAwADAAYAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAAAAD6//3//f/6//r/+v8DAP3/AAAAAAAAAAAAAAAAAwADAAAAAAAAAAAAAAAAAAAAAAAAAAMAAwAAAAAAAAAAAAMABgADAAAAAwAGAAAAAAAAAAYABgADAAMAAwAAAAMAAAAAAAMAAAADAAYABgAAAAYAAwADAP3/AAD9//3/AAAAAAMAAwADAAMAAAAAAAMABgAAAAMAAAAAAAMAAAAAAAAAAAAAAAAABgAAAAMAAwAGAAMAAwAAAPr/AAAAAAAAAAAAAAAAAwAAAAMAAAD9//3/AAAAAAAAAwAAAAAAAAAAAAMAAwADAAAAAAAAAAAAAAD9//3/AAAAAAMAAAADAAAAAAAAAP3/AAAAAAAAAAAAAAAA/f/9/wAA/f/9//3//f/9/wAA/f/6//T//f/3//r/AAAAAAAA/f8AAAAAAAAAAP3/AAAAAP3/AAAAAAAAAwAGAAAA/f8AAPr/+v/3//f/AAD9//3/+v/6//3/AAAAAAAAAAAAAP3/AAD9/wAAAwAAAAAAAAAAAP3/+v/9//r//f8AAAAAAAADAAAAAAAAAP3/+v/6//f//f/9//3//f/9/wAAAAADAAMAAAAAAP3//f8AAAAA/f8AAP3/+v/6//r//f/9/wAABgAAAAAAAAD9//3/AAAAAP3/AAAAAAAA+v/9//3/AAAGAAAABgAGAAYAAAAAAAAAAAAAAAAAAAAAAAYAAAAAAPr/AAAAAAAA/f/9/wAA/f8AAAAAAAAAAAAAAAAAAP3/AAD9/wMAAwAAAAAAAAD9/wAA/f/3//r/AAAAAP3/AAAAAAAA/f/9//f/9/8AAP3/AAD6//3/+v/9//3/AAADAAAAAAAAAAAA/f/6/wAAAAD9/wAAAAAAAAAAAwAAAAAAAwAAAAAA/f8DAAAAAAAAAAAAAAAAAAAAAAD9/wMAAwADAAkABgADAAAAAAD9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAAwAAAAAAAwAAAAMAAwAAAP3//f/9//3//f/6/wAAAAAAAAAAAAAAAAAABgADAAAAAAAAAAAA/f/9/wAAAAD9//r//f/6//3/AAAAAAAAAwADAAAAAAAAAAAAAAADAAMABgAAAAAAAwADAAAAAAAAAAAAAAAAAAMACQAGAAAABgADAAMAAwAAAAAAAAAAAAAA/f8AAAAAAwADAAAAAAAAAAAAAAAAAAAAAAAAAAAABgADAAMAAAAAAPr//f8AAAAA/f8AAP3//f/9/wAAAAD9/wMABgAAAAAAAAAAAAAAAAAAAAAAAAAAAP3//f/9/wAAAAD9//r/AAAAAAAA+v/3//f/+v/3/wAAAAAAAAAAAwAAAP3/AAD6//3/AAAAAAAA/f8AAP3//f/6//3//f8AAP3/AAD6/wAAAAD9//f/9//0//3//f/3//f//f/9//3/+v/6//3/AAADAAAAAAADAPr/+v/6/wAAAAD6//r//f/6//3/AAAAAAAAAAD9/wAAAAD9/wAAAAADAAAAAAD9/wAAAAAAAAAA/f8AAPf/9//0//f//f/9/wAAAAD6//r/+v/9/wAA/f8AAAAAAAADAAAA/f/6//3/+v/6//3/AAADAAYABgAGAAYAAwAAAAAAAAD9//r/+v8AAAAAAwAAAAAAAwAAAAYAAAAAAAAAAAD9/wAAAAD6//r/AAAAAAAAAAAAAAAAAAADAAMAAwAAAAAAAAAAAAAAAwADAP3/AAAAAAMAAAAAAAYABgADAAMAAAD9/wAAAAAAAAAAAAAAAAAAAAADAAYAAwAGAAYABgADAAkACQAGAAMAAwAAAAMAAAAAAAMABgAGAAMAAAAAAAAAAAAAAAMAAAAGAAMABgAGAAAAAwAAAAMAAwAAAAMAAAAGAAkABgAGAAYAAAAAAAMABgAGAAYAAAADAAAAAAAAAAYAAAADAAAAAwADAAMAAwAAAAAAAAAAAAAA/f8AAAAAAwAAAAMAAwAAAAYAAwADAAAAAwADAAMAAwADAAYAAAAGAAYAAAAAAAMACQAGAAkABgADAAMAAAAAAAAAAwAAAAYAAwAAAAAAAwAGAAMABgAGAAkAAwAGAAAAAAAAAAAAAAADAAkACQADAAAAAAAAAAMABgAGAAkAAwAGAAYABgADAAMAAwADAAAABgAGAAMAAwAAAAMAAAADAAMAAAAAAAAAAAD6//3/AAAAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAwAAAAAAAAAAAAAA/f/9/wAAAAAAAAMAAAAAAAAAAAD9/wAAAwAAAAAAAAD9//r//f/9//3//f/6/wAAAAADAAAAAAD6//r/AAAAAPr/+v8AAP3/AAAAAAAAAAD9//r//f/6/wAAAAD9//r/+v/9//3//f/9/wAAAAD9//r/+v8AAAAAAAAAAAAAAAAAAAAAAAD3/wAAAAD9//3//f/9//3/AAD9/wAAAAAAAAAAAAAAAAAAAAAAAAYAAAD3//r//f/9//3/AAAAAP3//f8AAAAA9//6//r/+v8AAP3//f8AAAAA/f/9/wAA+v/6//r/AAD9/wAAAAD6//r/AAAAAAAAAAAAAPr//f8AAP3//f8AAAAAAAAAAAAAAAD9//3/+v/6//3//f8AAAAAAAAAAAAAAAAAAAMAAAAAAAMAAwAGAAAAAAADAAkACQAGAAMAAAAAAP3/+v/6//r/+v/9/wAAAAD9//r/AAAAAAAAAwAAAAMAAAADAAAAAAADAAAAAAAAAAAAAwAAAAAAAAADAAAA/f/9/wAA+v/3//3/AAAAAP3/AAAAAAAAAAD3//r/+v/6//r/AAAAAAAAAAD9/wAAAAD9//3/AAAAAAAAAAAAAAAAAAADAAMAAAADAAAABgADAAYACQADAAAAAwAAAAAA/f8AAAAAAAAAAAAAAwAAAAAA/f8AAAAAAAAAAAAA/f8AAAAAAAD9/wAA/f8AAPr//f8AAAAABgAGAAAAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAD6/wAAAAAAAP3/AAAAAAAAAAAAAAAAAAADAAAAAAD9/wAAAwAAAAAAAAAAAAAAAAAAAAAAAwADAAYAAwAGAAMAAAAAAAAAAwADAAAAAwAAAAAA/f8AAAAAAAAAAAMAAwADAAAAAAAAAAAAAAD9//3//f8AAAAAAAAAAAAAAAAAAP3//f/3//3//f/6//r/+v8AAP3/AAAAAAAAAAAAAAAABgAAAAAA/f8AAAAAAAAAAAAAAAAAAAMAAwADAAAAAAAAAAMAAAAAAAAA/f8AAAAAAAAAAAMAAwAGAAMAAwAAAAAAAAD9/wAAAAAAAAYAAwADAAYAAAAAAPr/AAD6//3/AAAAAAMAAAAAAP3/AAAAAAMAAwADAAAAAAAAAAAA/f/9/wAAAAAAAAAABgAAAAAABgAGAAMAAAD9//r/AAAAAAAAAAAAAAAAAwAAAAAAAAD9//r/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwADAAAAAAAAAP3//f8AAAAAAAAAAP3//f/6//3/AAD9/wAAAAAAAAAA/f/9//r//f/9/wAAAAAAAAAAAAAAAPr//f/0//f/AAD9//3//f/9//3//f/9/wAAAAAAAP3/AAAAAAAAAAADAAAA+v/9//r/+v/6//r/AAD9/wAAAAD9//3//f/9//3/AAAAAPr/AAAAAAYAAwAAAAAAAAAAAP3/AAAAAP3/AAAAAAMAAAAAAAAAAAD6//3/+v/3//T//f/9//3//f8AAP3//f8AAAMAAAADAAAA/f8AAAAA/f8AAP3//f/9/wAA/f/9/wAAAAAAAP3/+v/6//3/+v/9//3//f8DAAAA/f8AAAAAAAADAAAAAAAAAAMAAAADAAMAAwAAAAAAAAADAAwAAwADAAAAAAD9/wAA/f8AAAAA/f8AAAAAAwAAAAMABgAAAAAAAwAAAAAAAAD9/wMAAAD9/wAAAAD9//3/AAAAAP3//f/9/wAAAAAAAAAA/f8DAAAAAAD9/wAAAAD9//r//f/9/wAAAAAAAAAA/f/9/wMAAAD9/wMAAAAAAAAAAAD3//3//f/9/wAA/f8DAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAADAAYAAwADAAMAAAD9/wAAAAAAAP3/AAAAAP3/AAAAAAAA/f8AAAAAAAADAAMAAwAAAAAAAAAAAAAAAAD9//r//f8AAAAA/f/9/wAA/f/9//r//f8AAAMAAwADAAAAAwADAAAA/f/9/wAAAAD9//r/AAD9/wAAAAD9/wAA/f8AAAAAAAD9/wAAAAAAAAAAAwADAAAAAwAGAAAAAAD9/wAA/f8AAAMABgAGAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAAAABgAGAAYAAwAAAAAA/f/9/wAA+v8AAAAAAAAAAAAAAAD9/wAAAAADAAYAAAAAAPf/9//6/wAAAAD9/wYABgAAAP3/AAAAAP3//f8AAP3//f8AAAAAAAD9/wAAAAAAAP3/AAD6//3/+v/6//r//f/6/wAAAAAAAAAAAAD9//r/+v/3//r//f8AAAAA+v8AAP3/+v/6/wAAAAD9//r//f/3//r//f/0//f/+v/3/wAA/f/x//T/+v/6//r/+v/6//r//f/9//r/+v8AAP3/+v/6//3/AAD6//r//f/3//r//f/9//3/+v/6/wAAAAD9/wAAAAADAAAAAAD9/wAA/f/6/wAA/f8AAPf/9P/x//T/AAD9//3//f/6//r/9//6//3/+v/6//3/+v8AAP3/+v/6//3/+v/6//3//f8AAAAAAAAAAAAAAAD9//3//f8AAAAAAAAAAAMAAwAAAAAAAAAAAAAA/f/9/wAAAAD9/wAAAAD3//T/AAAAAAAAAAADAAMAAAAAAAAAAAD9//r//f8AAAMAAwAGAAAAAwADAAMAAAD9/wAAAAAAAAAAAAAAAAAAAAADAAAAAAAAAAAAAAAAAAMAAwAJAAMAAwAAAAMAAAAAAAAAAAAAAAMAAAAAAAMAAwADAAAAAAADAAAAAwADAAYAAAAGAAMAAwAGAAMABgAGAAYABgAGAAYAAwAGAAMAAAAAAAAAAAADAAMAAwAAAAMAAAADAAAAAAADAAMAAAADAAMAAwAAAAYABgADAAAAAwAAAAMAAAD9/wAAAwAAAAAAAwAAAAYAAwAAAAMAAwADAAYACQAGAAkAAwAGAAAAAAAAAAMACQAJAAwACQAGAAkABgADAAAAAwADAAkABgAAAAAAAwAGAAYABgAGAAkAAAADAAYAAAAAAAAAAwADAAYABgAGAAYABgADAAYACQAJAA8ACQAMAAwACQAGAAMAAwAGAAkADAAJAAYABgAGAAYAAwAGAAMAAwAAAAMAAwAAAAAAAwAAAAMAAwADAAAAAAAAAAYAAwADAAMABgADAAAAAwADAAAAAAAAAAYAAwAGAAMABgAAAAAAAAD9//3/AAAAAAAAAAAAAAAAAAAAAAMAAwAAAAAAAAADAAAAAAD9/wAAAAAAAAAAAAADAAAAAAAAAAAAAwAAAAAA/f8AAAAAAAAAAP3/AAAAAAMAAAAAAAAAAAD6//r/9//6/wAAAAAAAAAAAAAGAAYAAAAAAAAAAAD9//r//f/9/wAAAAD6//3/AAAAAAAAAAAAAP3/AAAAAAYAAAD3//f//f/9/wAA/f8AAAAABgADAAAAAAAAAAAAAAD9/wAAAAAAAAAA+v/3/wAA+v/9//3/AwAAAAAAAAD9//3/+v/9/wAA/f8AAP3/AAADAAMAAwAAAAAAAAAAAAAA/f/9//3//f/9/wAAAAAAAAAAAAADAAAAAAAAAAAAAAD9/wMAAwADAAAAAwADAAYABgAGAAYAAAD9/wAA+v/3//3/+v8AAAMAAAD9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAA/f/9/wAAAAAAAAAAAwAGAAMA/f8AAAAA/f8AAAAAAAAAAAAAAAAAAP3//f8AAP3//f/9/wAAAAADAAAAAAAAAAAAAAD9//3/AAAAAAAAAAADAAMABgADAAMAAwADAAAAAAAAAAMAAAD9//r/AAAAAAAA/f/6//3/AAD9//r//f/6//3//f8AAAMAAwAAAAMAAAAAAAAAAAD9/wAAAAAAAP3//f/9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAwADAAAAAAAAAAMAAAAAAAMAAAAAAAAA/f/9/wAA/f/9//3/AAAAAAAAAAADAAMAAAAGAAMAAAD9/wAAAwAAAAAAAAAAAAAAAAADAAMAAwAGAAMAAAADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAADAAAAAwAGAAYACQADAAAAAAAAAAAAAAAAAP3/+v/9/wAAAAAAAAAABgAAAAAA/f/3//r/+v/9//3//f8DAAAAAAAAAAMAAAADAAMABgAGAAMAAAAAAP3//f/9//3/AAADAAYAAwAGAAMAAAAAAAYACQADAAMAAwAGAAAAAAAAAAkACQAGAAMAAwAAAAAAAwAAAAAAAAAAAAkABgADAAYABgADAAAAAAD6//3/AAAAAAMAAAAGAAAAAAAAAAMAAwAAAAAAAwAGAAYAAwAAAAAAAAAAAAMAAwAAAAAAAAADAAMABgAAAPr//f/9/wAA/f8AAP3/AAAAAAAAAAD9//r/AAAAAAAAAwAAAAMAAwAAAAMAAwAAAAAAAwADAAMAAwAAAAAAAAAAAAAA/f8AAAAAAwAAAAAAAAD9/wAAAAAAAAAAAAD9//3/+v8AAP3/AAD9/wAAAAAAAP3/AAAAAAAAAwAAAAAAAAAAAP3//f8AAAAAAAADAAAAAwAAAAAAAAADAAAA/f/9//r/9//3//r//f/9//3//f/9//3/AAAAAAAAAAAAAP3/AAAAAAAAAwAAAAAAAAADAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAD9//r/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAP3//f/9/wAA/f8AAAAA/f8AAAAAAAAAAAAAAwADAAAA/f/9//3/AAAAAP3//f8AAAAA+v/9//3/AAADAAAAAwAAAAMAAAAGAAMAAwAAAAAAAAAAAAYAAAADAP3/AAAAAAAAAAAAAAMAAAAAAAAAAAAAAAAAAAD9//r/AAD9/wAAAAD9/wAAAAAAAAAAAAD9//r/AAD9//3//f/6/wAA/f8AAP3//f8AAP3/AAD9//3//f8AAAAAAAADAAMAAAAAAAAA/f/9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAMABgADAAYAAwADAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwADAAMAAAAAAAAAAwADAAYABgAAAAAAAAAAAAAA/f/9/wAAAAAAAP3/AAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAD6//r/AAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwADAAAAAAAAAAAA/f8AAAMABgAGAAAABgADAAMAAwADAAYAAwAAAAAAAAAAAAAABgADAAAAAAD9//r//f8AAAAA/f8AAAAAAAAAAAAAAAAAAAAAAwADAAMAAAAAAPr//f/9/wAAAAAAAAYABgADAP3//f8AAP3/AAAAAAAA/f8AAAAA/f/6/wAAAAAAAP3/AwAAAAAA/f8AAP3/AAD6/wAAAwAAAAMABgAAAP3//f/6//3//f8AAAAAAAADAAAA/f/6/wAAAAAAAAAAAAD9/wAAAAD6//r/+v/3/wAAAAD3//f//f/9//3//f/9//3//f8AAAMAAAADAP3/AAD9/wAAAAD9/wAAAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAADAAAAAwD9/wAA/f8AAAMAAAADAP3/AAD9//r//f/6//3//f/6//3/+v/9/wAAAAAAAAAAAAADAAAAAAAAAAAA/f/9/wAA+v/9/wAAAAADAAMABgAGAAAAAAAAAAAAAAAAAAMAAwADAAAAAAADAAAAAAAAAAAAAwADAAMAAwD9//r/AAD9//r/+v8AAAAAAAADAAAAAwAAAP3/AAAAAAMAAAAAAAAAAAADAAMAAwAAAAMABgAAAAAAAAD6//3//f8AAAAAAAADAAAAAwADAAMAAAAAAAAAAAD9/wMAAwAAAAMABgADAAkAAwADAAMAAwADAAMAAAAAAAAAAAADAAMAAAADAAAAAwAJAAYABgADAAMAAwADAAMAAAAGAAYAAAAAAAAAAAAAAAMABgAGAAYAAwAGAAAAAAAAAAMAAAAGAAMABgAGAAkABgADAAAABgAAAAMAAAD6//3/AAD9/wAABgAAAAYABgAGAAMAAAADAAYABgAJAAYAAwAJAAMAAAAAAAYAAwADAAMABgADAAkABgADAAMAAwADAAkABgAAAAAAAwADAAMAAwAGAAYAAwAGAAMAAAAAAAAAAAADAAYACQAGAAYACQAJAAYABgAGAAkAAAADAAYABgAGAAYABgAGAAMABgADAAMAAwAAAAAAAAADAAMAAAAAAAMAAAD9//3/AAAAAAAAAwADAAMAAwAGAAkAAwADAAAAAAAAAAAAAwAAAAAA/f8AAAYAAwADAAAAAwAAAAMAAAD9//3/AAAAAAMAAwAAAAAAAAAAAAAAAAAAAAAAAwADAAAA/f/6//r/AAAAAAAAAAADAAAAAAAAAAAAAAAAAAAA/f8AAAAAAAAAAAAAAAAAAAAA/f8AAAAAAAD9//3//f8AAAAAAwADAAAAAAAGAAMAAAAAAAAAAAAAAP3/AAAAAAAAAwAAAAAAAwAAAAMAAAAAAP3/AAAAAAMAAAD9/wAAAAAAAAAAAAADAAAABgAGAAMAAAAAAAAAAAAAAAMAAAADAAAAAAD9/wAA+v/6/wAAAAAAAAAAAAAAAP3/AAAAAAMAAAADAAAAAAAAAAMAAwADAAMACQAAAAAA/f/9//r//f/9/wAAAAADAAMAAAADAAAAAwADAAMAAAAAAAMAAwAGAAAAAAADAAYACQAGAAYAAwAAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAwADAAAAAwADAAMAAAAAAAAAAAAGAAMAAAAAAAAAAAAAAAAAAAADAAAAAAAAAAAAAAAAAAMABgAGAAMAAAAAAAAAAAAAAPr/+v/9/wAAAAADAAMAAwAAAAAAAwD9//3/AAAAAAAAAAAAAAAAAAADAAYABgAGAAMAAwADAAMAAwAAAAAAAAAAAAMAAAAAAAAABgAAAAMAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAA/f/9/wAAAwADAAAAAAAAAAAAAAADAAMAAwADAAAAAAAAAAAAAwAAAAMAAAAAAAAAAAAAAAAA/f/9//f/+v8AAAAAAAADAAYAAwAGAAMAAwAAAAAAAwAAAAAAAwAAAAAAAAAAAAAAAAAAAAMAAwAGAAMAAwAAAAAAAAAAAAAABgADAAAAAAADAAAAAwADAAMAAwAAAAAAAwAGAAYAAAAAAAAAAAAAAAMAAwAAAAAABgAAAAAAAAD6//r//f/9//r/+v8AAAAAAAAAAAAAAAADAAMACQAGAAMAAAAAAAAA/f/9//3/AAADAAMABgADAAAAAAAAAAYAAwADAAAAAwAJAAYAAwAAAAYAAwAJAAYABgADAAMABgAAAAAAAAAAAAMAAwAAAAYAAwADAAAAAAAAAAAAAAAAAAAAAAAGAAAAAAAAAAMAAwAAAAAAAwADAAYABgAAAAAAAAAAAAAAAAAAAAAAAAADAAAAAwAAAAAAAAAAAAAAAAAAAP3/AAAAAAAA/f/9//3/AwADAAYABgAAAAAAAAD9/wAAAwAAAAAAAwADAAAAAAAAAP3/AAAAAAAAAAADAAAAAAAAAAAAAAAAAAAAAwADAAAAAAD6//r/+v/6//r//f/9/wAAAAAAAP3/AAD9/wAAAAADAAAAAAAAAAAAAAAAAAAAAAAAAP3/AAAAAAAAAAADAAAA/f8AAP3//f/6//3//f/3//r//f8AAP3/AAADAAAAAAAGAAMAAwAAAAAAAwAAAAAAAAAAAP3//f/9//3/AAAAAAYABgADAAAAAAAAAAAA/f/6//r//f/6//r/+v8AAAAAAAAAAAAAAAAAAP3/AAADAAYAAAADAAMAAAAAAAAAAAAAAAAAAwADAAAA/f8AAP3//f/9//r/AAADAAAAAAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAwAAAAAAAAADAAkAAAAAAP3/AAAAAAAA/f8AAAAA/f8AAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAD3//r//f8AAAAAAAD9//3/AwAAAAAAAAD9/wAA/f8AAPr/+v8DAAAA/f/6//3/+v/9//3/AAAAAAAAAAAAAAAA+v/6//3/AAAAAAAAAAAAAP3/AwAAAAAAAwAAAAMAAAADAAAAAAAAAAAAAwAAAPr/+v/3/wAAAAAAAAAAAAADAAMAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAwAAAAAAAAAAAAAABgADAAYAAwAAAAAA/f8AAAAAAAAAAAMAAwAAAAAAAAD9//3/AAAAAAAAAAAAAAAA/f/6//3/AAAAAP3/AwAAAAMAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAADAAAAAAAAAAAAAAAAAAMAAwAGAAAAAwAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAwADAAMAAwAAAAAA/f8AAAAA/f8AAP3/AwADAAAAAAAAAAAAAAADAAMAAAAAAPr//f8AAAMAAwAAAAkABgADAAAAAAAAAPr/+v8AAP3/+v8DAAAAAAAAAAYAAwAAAAAAAwAAAAAA/f/9//f//f/6/wMABgAGAAMAAwAAAAAAAAD9/wAAAwAGAAMAAAAGAAAAAAAAAAAA/f8AAP3/AAD6/wAAAAD9//r//f/3/wAAAAD6//3/AAAAAAAAAAAAAAAAAAADAAAA/f8AAPr//f/6/wAAAAAAAAAAAwAAAAAAAAAAAAMAAAAAAAMAAAAAAAAAAAAGAAMABgAAAAMAAwAAAAAAAAADAP3/AAD6//3/AAAAAAAAAAD9//r//f8AAAAAAAADAAAAAAAAAAAAAAD9/wAAAAAAAAAA/f8AAAYAAwADAAMABgADAAAAAwAAAAAAAAAAAAAAAwAAAP3/AwADAAYAAwAAAAMAAAAAAAAAAAD6//3/AAAAAAAA/f8AAAAAAAADAAAAAAAAAP3//f/9/wAAAAAAAP3/AAAAAAMABgADAAYABgAAAAAAAAAAAAMAAwADAAAAAAAAAP3/AAAAAAMAAwAGAAMABgAAAAYABgADAAMAAwADAAYAAAADAAMABgAGAAAAAAAAAAAAAAAAAAAAAAADAAMAAwADAAAAAwAAAAMAAwADAAMAAwAGAAYAAAAAAAAAAAD9/wAAAwADAAMAAwADAAAAAAAAAAAAAAADAAMABgAGAAYAAwAAAP3/AAAAAAAAAAAAAAAAAAD9/wAAAAD9/wAAAwADAAAAAAADAAMAAwADAAMAAwAJAAYAAAADAAAAAwAAAAMABgAAAAMAAwADAAAAAAAAAAAAAAD9//3/AAAAAAAAAAADAAMAAAADAAAAAAAAAAAAAAAAAAAAAAAAAP3/AAAAAAAAAwADAAYAAAAAAAAAAAAAAAAAAwAGAAAAAwAGAAYAAAAAAAAAAAADAAAAAAAAAAAAAAD6//3/AAD9//3/AAAAAAAAAAADAAMAAAAAAAAAAAAAAAAAAAAAAAAA/f8AAAAAAwAGAAYAAwADAAAAAAD9//3/AAAAAAMAAAAAAP3//f/9//3//f/6//3/AAAAAP3//f/3//T//f/9//r/+v8AAAAAAAAAAP3/AAAAAP3/AAD9/wAAAAD6//r/AAAAAAAAAAAAAAMA/f/9//3//f8AAAAAAAAAAP3/+v/9/wAAAAD9/wAAAAAAAP3/AAD9//3/AAD9/wAA/f8AAAAAAAAAAP3/AAAAAAMAAAD9//3/AAD9//3//f8AAPr/+v/6//r/9//6//r//f8AAAMAAAAAAAAAAAAAAAAA/f/9/wAAAwAAAAMAAAD9//r//f/9//3/AAADAAAAAAAAAAAA/f8AAAAAAAAAAAAAAAAAAAAAAAD9/wAA/f8AAAAAAAADAAMAAwADAAYAAAAAAAMABgAJAAYACQAJAAkACQADAAYAAAAAAAAAAAD9//3//f8AAAAAAAD9/wAAAwAAAAAAAwAAAAAAAAADAAMAAAAAAP3/AAD9/wAAAwAAAAAAAAADAAAAAAAAAAAA/f8AAAMAAwAAAAAAAAAAAAAAAAD0//f/+v/6//r/AAAAAP3/AAAAAAAAAwADAAAAAwAAAAAAAwADAAAAAwAAAAMAAwADAAAAAwAAAAMABgAGAAMABgAGAAkABgAGAAMAAwAAAAAAAAAAAAAA/f8AAAAAAAAAAAAAAAAAAAAA/f/6/wAAAAADAAAAAwAAAP3/AwAAAP3/AAAAAAAAAAADAAMAAAAGAAAAAAD9/wAAAAAAAAAAAAADAAAAAAD9/wAAAAD9//r//f/9/wAAAAAAAAAAAAADAP3//f/6//3//f/9/wAABgAGAAMAAAAAAP3/AAAAAAMAAAAAAAAAAAD9/wAAAAAAAAAAAAAAAAAAAAADAAMAAwAAAAAAAAAAAAAAAAAAAAAAAAD9/wAA/f8AAAAAAAAAAAAAAAAAAP3//f/6//r/+v/6//f//f8AAAAAAAAAAAAAAAADAAAAAwAAAAAAAAAAAP3/AAD9//3//f8AAAMAAAADAP3/AAAAAAMAAAAAAAMAAwAGAAAAAAAAAAMAAwAAAAAAAAD9//3//f/6//r//f8AAAAAAAD9/wAAAAAAAP3/AAD9//f/+v/9/wAAAAAAAP3//f/9/wAAAAAAAP3//f/9/wAA/f/9/wAAAAAAAP3/AwD9/wAAAAADAAAAAAD9//r/AAAAAAAAAAAAAAAAAwAAAAAAAAD9//r/AAAAAAAAAAAAAAAA/f8AAAMABgADAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP3/AAD9//3/AAAAAAAAAAAAAPr/AAD9//3/AAD9/wAA/f/9//r/AAD9/wAAAAAAAP3//f8AAAAAAAAAAAAAAwAGAAAAAwAAAAAAAAAAAP3/+v8AAP3//f/9//3/AAAAAAAAAAAAAP3/AwADAAMAAwADAAAAAAAAAAAAAAAAAAAAAAD9//3/AAAAAAAAAwADAAMAAAAAAP3/AAD9//3/+v8AAP3/AAAAAAAAAAADAAAAAAAAAAAAAAAAAP3/AAADAAAAAAAAAP3/+v/6//r//f8AAAMABgADAAAAAAD9//3//f8AAP3//f8DAAMAAAADAAAAAAAAAAAAAwAGAAMAAAAGAAAAAAD9//3/AAAAAAkABgAGAAMAAwADAAAAAwAGAAYAAwAAAAAAAAAAAAAAAwADAAAAAwAAAAYAAwAAAAAA/f8AAAAAAAD6//r/AAAAAPr//f8AAP3//f8AAP3//f8AAAAAAAD9/wAAAAAAAP3/AwADAAAAAAADAAAA+v/6//r//f/6/wAAAAAAAPr/AAAAAAAAAAAAAAMAAAAGAAAAAAAAAAAAAAD9//3//f/3//3//f8AAAAAAAAAAAMAAwADAAAAAAAAAPr//f/9//3/AAADAAMAAAADAAAAAAAAAAAAAAAAAAAABgADAAMAAwAAAPr/AAAAAAAA/f/6//3/+v/9//f//f/9//3/AwADAAAAAAADAAAAAAAAAAAAAAD9//r/AAAAAAAAAAD6//r/+v/9//r//f/9/wAA/f/9/wAA/f/9//3/AAADAAAAAAD9//3/AAD9/wAAAAAGAAAAAwADAAAAAAAAAP3/AAAAAAAAAAAAAAAABgAGAAMAAAAAAP3/AAD9/wAA/f8AAP3/AAAAAAAAAAAAAAAAAAAAAP3/AAAAAP3/AAAAAAAAAAAAAAYABgAAAP3/AAAAAP3/AAADAAAAAAAGAAAAAAAAAAAAAAAAAAAAAwADAAYAAAAAAP3//f/0//r//f8AAAAAAAAAAP3//f/9//3//f8AAAAAAAADAAAAAAAAAAAAAAAAAP3/AAD9/wAAAAD6//f//f/6/wAA/f/0//r/AAAAAPr//f/6//r//f/9/wAA/f8DAAAAAAAAAAAAAAD9//r/AAD9/wAAAAAAAAAA/f/9//r/+v/6/wAA/f8DAAAAAAAAAAAAAAAAAAYAAwADAAAAAAD6//r//f/9//r/AAD9//3/+v/6//3/+v/9//3/AAAGAAAAAAAAAAAAAAAAAP3//f/9//3/AAAAAAAABgADAAAAAAADAAAAAwADAAMAAAADAAAAAAAAAP3/+v/6//r/AAAAAAAAAAD6//r/AAADAAAA/f8AAAAAAwAAAAAAAAD9//f//f/9/wMABgADAAAAAAAAAAAAAAAAAAAAAAAAAP3//f8AAAAAAAADAAAAAAD9//3/AAAAAAAAAwADAAAAAAAAAAAABgADAAAAAAD9/wAAAAADAAMAAwAGAAMAAAAGAAAAAwAGAAkAAwADAAAAAAADAAAAAAAAAAAAAwAAAAAAAAAGAAAAAAAAAAAAAwADAAMAAwAGAAMAAwAGAAAAAAAAAAAAAAADAAMAAwADAAMAAwAAAAAAAwADAAYA/f8AAP3/AAAAAAAAAwADAAMAAwADAAAAAAADAAMACQAJAAkABgAGAAAAAAAAAAAAAAAAAAAAAAAAAAMAAwADAAAAAAADAAYACQADAAYACQAGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAGAAYABgAJAAMABgADAAMABgAGAAkAAwADAAMAAwADAAAAAAAGAAYACQADAAYABgAJAAkABgAJAAMAAwAAAAAAAwAAAAAABgAAAAAAAAADAAAAAAAAAAAAAAAGAAAAAAAAAAAAAAAAAAAAAwADAAYABgAGAAAAAwADAAAAAAAAAAAAAwAAAAAAAAADAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAD9/wAABgAJAAAAAwADAAAAAwAGAAAABgAGAAMAAwADAAAAAAADAAAAAAAAAAAA/f8AAAAAAAAAAAAA/f/9/wAAAAAAAP3/AAAAAAAAAAADAAYABgADAAAAAwAAAAAAAAD9//3/AAAAAAMAAwAAAP3/AAAAAAMAAAAAAP3/AwAAAAAAAAADAAAACQAMAAkABgADAAMABgADAAAA/f8DAAAAAAD9/wAAAAADAAMACQAGAAkAAwAAAAAAAAAAAAMAAAAJAAAAAwADAAMAAAAAAAAABgADAAAA/f8AAP3//f/9/wAAAAAAAAAAAAADAAMAAwAGAAAABgAGAAkABgAGAAMABgAGAAkADAAMAAwABgADAAMAAAD9/wAAAAADAAkABgAGAAMABgAJAAAAAwADAAYAAwADAAAAAwAGAAAAAAAAAAMABgAGAAMABgAGAAMABgADAAkAAAAAAAAAAAAAAAAAAwAGAAMABgAGAAMAAwADAAkABgAMAAkACQAJAAYAAwAAAAAAAwAAAAMAAwAAAAAAAAAAAAAAAAADAAAAAwAAAAAAAAAAAP3/AAAAAAYAAwAAAAAACQADAAMAAwD6//r/AAAAAAAAAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMABgAGAAAAAwAGAAMAAwADAAMABgAGAP3//f/9//3//f8AAAMAAAADAAAAAAAAAAAAAwAAAAAAAAAAAP3/AAAAAAAAAAADAAMAAwADAAYABgAGAAMABgADAAMABgAGAAMAAwADAAMAAAAAAAAAAAAAAAMAAwAAAAAAAAADAAYAAwAGAAAAAwADAAMAAwADAAMAAwADAAMAAAADAP3/AAAAAP3/AAAAAAAAAwADAAMAAwAAAAAAAAADAAAAAAAGAAMAAwADAAYABgAGAAMACQAGAAMAAAADAAMAAAADAAMAAwADAAMABgAAAAMAAAAAAAMABgADAAMABgAJAAMABgAGAAkACQAJAAMABgAAAAMAAwAAAAMAAwADAAMAAwAAAAAABgADAAAAAAAAAAAAAAADAAMABgAPAAkACQAMAAkABgADAAAAAwAAAAAAAAAAAAMABgADAAAAAAAAAP3/AAADAAAAAwAAAP3/AAAAAAAAAwAGAAAABgADAAAAAAD9//3/AwADAAYAAwAAAAAAAAAAAAYABgADAAAAAAAAAAAAAAAAAAAAAAADAAMAAwAJAAYACQAGAAAAAAAAAAAAAwAAAAAAAAD9//3/AAAAAP3/AAD9/wAA/f8AAP3/AAAAAAAABgAGAAAAAAAAAPr/AAAAAAAAAAAJAAYABgADAAMAAwADAAAAAAAAAAAA/f/6//r//f/9/wAAAwADAAAAAAADAAAAAAADAAAAAAAAAAAAAwADAAAAAwADAAAAAAADAAAAAwAGAAkABgAAAAAAAAAAAAMAAwAAAPr/AAD9/wAA/f8AAAAAAAAGAAYABgADAAAAAwADAAMAAwAGAAAAAAAAAAAAAAADAAAAAwAGAAAAAAAAAAAAAAAAAAAAAAAGAAAAAAAAAAAAAAADAAAAAwAAAAMAAAAGAAMAAwAAAAAAAAAAAAYAAwAAAP3/AAADAAYAAwAJAAkAAwAGAAMAAwADAAMABgADAAMABgAAAAMAAAAAAP3//f/9//r//f/3//f/AAAAAAAAAAAAAAAAAAAAAPr/+v8AAAAAAAAAAAAAAAAAAP3/AwADAAMAAwAJAAYAAAAAAAAAAwAAAAAAAAADAAMAAwADAAAAAwAAAAYABgAGAAAAAwADAAAAAAAAAAAAAAAAAAAAAAD9/wAAAAADAAMAAwADAAAAAAADAAMAAwADAAMAAAADAAYAAwAGAAMAAwADAAMAAwAAAP3/AwADAAYACQAGAAMAAAADAAMAAwAAAAAAAAAAAAAAAAAAAAAABgAGAAAAAAAAAAAAAAAAAAAAAwADAP3/AAAAAAAAAAD9/wAAAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAABgAJAAAAAAADAAAAAAAAAAAABgAGAAAAAwAAAAMAAwAGAAkABgAGAAYAAAAAAAAABgAGAAMAAwADAP3/AAAAAAAA/f8AAP3/AwADAAYAAwAAAAAAAwAAAAAAAAAAAP3/AAAAAAAAAAADAAkABgAGAAAAAAAAAP3//f8AAAAAAAAGAAMAAwAAAAAAAAAAAP3/AwAAAAMAAAAAAAAAAAD9/wAAAwADAAYAAwAAAAAAAwAAAAMABgAJAAYABgAMAAMAAwADAAMAAwADAAAAAAAAAAMAAwAAAAAAAAAAAAMAAwAAAP3/AAAAAP3//f8AAAAAAAADAAMAAAAJAAAAAwADAAYABgADAAMABgADAAMAAwADAAMAAAAAAAMAAwADAAkABgAMAAkACQADAAMABgADAAMAAwAJAAAAAAAAAAAAAwADAAMAAwAAAAAA/f8AAAAAAAADAAAAAwAGAAYAAwADAAMAAwADAAAAAAAGAAYABgAGAAMAAwADAAAAAAAAAP3/AAAAAAMAAwADAAAAAwAGAAMAAAAAAAAAAAADAAAAAAAAAAAABgADAAAA/f8AAAAAAAADAAAAAAAAAAAAAwAAAAYABgAGAAAAAAAGAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAMAAwAJAAMAAAADAAMAAwAGAAMAAwAAAAAAAwADAAYACQAGAAkAAwAGAAMAAwADAAAAAAAAAAAAAAAAAAMAAAADAAAAAwAGAAMABgAGAAYABgAJAAkAAwAGAAMAAAADAAMAAwADAAMABgAGAAYAAwADAAAAAAADAAYAAAAGAAMABgADAAMAAAAAAAAAAwADAAYAAAAAAAAAAAD9/wAAAwADAAYACQAGAAMAAwADAAMABgAMAAwABgAMAAYAAwAAAAAAAwAAAAMAAwAAAAYABgADAAAAAAAAAAMAAwAAAAAAAwADAAAAAAAAAAMAAAAGAAYAAAADAAAAAwAAAAMABgADAAAABgAGAAYACQAGAAkAAwADAAMAAAADAAAAAAAGAAMABgAGAAYAAwAAAAAAAAAGAAAAAwADAAMAAwAAAAAAAAAAAP3/AAAAAAAAAAAAAAMABgAJAAYABgADAAAAAAADAAAAAAADAAYACQADAAAAAwAAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAP3//f/9//3/AAAAAPr//f8AAP3/AAADAAAAAAADAAAAAAAAAAAAAAADAAAAAAAAAAMAAAD6//r//f/9/wAAAAADAAMA/f8AAAAAAAADAAAAAwADAAAAAAAAAAAAAAAAAAMABgAGAAMABgAAAAAAAAAAAAAAAAAAAAAAAAADAAAAAAAGAAkABgAAAAAAAAAAAAAAAAADAAAAAwADAAAAAAADAAAAAwADAAMAAAAAAAAA/f8AAAAAAAAAAAAABgADAAYABgAAAAAAAwAAAAAAAAAGAAAAAwAGAAMABgAGAAMABgADAAMAAwAGAAMAAAD9/wAAAAADAAYACQAJAAkABgAGAAkABgAGAAwACQAPAAkADwAPAAwADwAJAAYAAAAAAAAAAAAAAAAAAAAGAAkABgADAAAABgADAAYACQAGAAMAAAAAAAAAAAADAAAAAwADAAkACQADAAAAAAAAAAAA/f8AAAAAAAAAAAAAAwADAAAAAAADAAMAAAD9//r/AAAAAAAAAAADAAAAAAAAAAAAAwAAAP3/AAAAAAAAAAADAAMAAwADAAYABgAGAAMABgAGAAYAAwAGAAAAAwADAAYABgAJAAMAAwAAAP3/AAADAAMABgADAAYACQAAAAAA+v/9//3/AAD6//r/+v8AAAAAAAD9//3/AwADAAAAAwAGAAAAAAAAAAMAAwADAAAAAAADAAYABgADAAAA/f8AAP3/AAAAAAMAAwADAAAAAAAAAAAAAwADAAMAAAAGAAAAAAAAAAAAAwAAAAAAAAD9//3//f8AAAAAAwAGAAYABgAGAAMAAAAAAAAAAAADAAAAAAAAAAMAAAAGAAMABgAJAAYABgAJAAYACQAGAAkAAwAGAAYAAwAGAAYAAAAAAAAAAAAAAAAAAAD9//3/AAAAAAAAAwAMAAMAAwAGAAYABgAGAAMABgAAAAMAAAADAAAAAwADAAAAAwADAAYAAAAAAAAAAAADAAYABgAGAAwACQAMAAYAAwADAAYACQAJAAkACQAGAAYABgAAAAAA/f8AAAAAAAAAAAAAAAAAAP3/AAD9//r//f8AAAAAAwAJAAYACQAJAAYABgAGAAMAAAADAAYAAwAAAAYAAwADAAAABgAAAAAAAAAGAAMAAwAAAP3/AwAAAAAAAwADAAAAAwADAAAAAAAAAP3/AAAAAAMABgAGAAAAAAAAAAkACQAGAAMABgADAAMAAwAAAP3/AAAAAAMAAwAGAAYABgAGAAAABgADAAAAAwADAAMA/f8AAP3/AAAAAAAAAAAAAAAAAAADAAAAAAAAAAAAAwADAAAAAAADAAAAAAAAAAAAAAAGAAAAAwAAAAMABgAGAAMAAAAAAAAAAAAAAAAAAwAAAAMAAwAAAAAAAwADAAMACQAJAAAAAwADAAYAAwADAAMAAwAAAAMABgADAAMABgAGAAYAAAAAAAAAAAD9/wAAAAAAAAAAAAADAAYAAAADAAAAAAADAAYACQAGAAYADwAPAAkAAwAGAAAA/f/9//3/AAAAAAMABgAAAAMAAwADAAAAAAADAAAAAAAGAAMAAAADAAMAAwAGAAMADAAMAAwAAwAMAAYAAwAAAAAAAAAGAAwABgAJAAYABgAJAAMABgAGAAkABgAGAAMABgADAAYACQAJAAYADAAGAAwADAAGAAkAAwADAAMAAwAAAAAAAwADAAAAAwAAAAAAAAADAAAAAwAJAAMABgADAAYABgAGAAMAAwADAAAAAwAGAAMAAAAAAAMABgADAAkAAwAJAAAABgADAAMAAwAAAAMAAAAJAAAAAwAGAAMABgAGAAMAAwAAAAAAAAAAAAMAAwADAAkADAAMAAkABgAGAAAAAAAAAP3/AAAAAAYAAwAGAAAAAwAGAAYACQAGAAYACQAGAAYABgAAAP3/AAAAAAAAAAAAAAAAAAAAAP3/AAAAAAAAAAADAAMABgAGAAMAAAAAAAAAAAAAAP3/BgAGAAkAAwD9//3//f8AAP3/AAAAAAMAAAAAAAMAAAAAAAAABgAGAAAAAAAAAP3/AAAAAAMABgADAAAABgADAAAAAwADAAYABgAGAAYAAAADAAAABgAJAAYABgADAAAAAAD9/wAA/f8GAAAABgAJAAYAAwAAAAAAAwADAAYAAAAJAAMAAAADAAMAAwAAAAYAAwADAAAAAwAGAAMABgAJAAMAAAADAAMAAAAGAAkACQAJAAAABgADAAYAAAAAAAAAAAD6/wAAAAADAAAAAwAAAAAAAAAAAAMAAwAJAAwABgAMAAMAAAAAAAMAAwADAP3/AAAAAAYABgAAAAAABgADAAMAAwAAAAAAAwAAAAAAAAAAAP3/AAAAAAAAAwAJAAMACQAGAAYABgADAAAAAwAAAAMABgAGAAYAAwADAAAAAAD9/wAAAAAGAAMABgAGAAYACQAGAAwABgAGAAAAAAAAAAAABgAAAAAAAwAAAAAA/f8AAAAAAAAAAAMAAwAJAAMAAwADAAkAAwAAAAAAAAAAAAYABgADAAAABgADAAAAAwADAAAABgAJAAwACQAMAAYAAwADAAMAAwAAAAAAAAAAAAAAAAD9//r/AwAAAAAAAAADAAMAAwADAAMAAwADAAAAAAAAAAkACQAGAAMABgAGAAMAAwAAAAAAAAAAAAMAAwADAAAAAAAGAAAAAwADAAAAAwAAAAMABgAJAAMABgADAAkACQAJAAkACQAGAAkACQAJAAkACQAGAAMAAwAGAAYACQAGAAkAAwADAAMACQAJAAkACQAJAAYACQAGAAwABgAMAAkAAwAGAAYABgAJAAMABgADAAYABgADAAMAAAAJAAMAAwAMAAkACQAGAAMAAwAAAP3/AwADAAkAAwAGAAYACQAAAAAABgAGAAMAAAADAAAAAAAAAAMADAAMAAwACQAMAAYAAwAGAAYABgAGAAAABgADAAwADwAMAAYACQAJAAwADAAGAAYABgAJAAYACQAGAAMABgAJAAMABgAGAAAABgAGAAwACQAMAAwADwAJAAkACQAJAAwADAAJAAkACQAJAAYAAwAJAAwADAAGAAYACQAJAAwADAAPAAkACQAGAAMABgADAAYADAAGAAYABgAGAAAAAAADAAkABgAPAAYABgADAAAAAAAAAAAAAAAGAAkABgAJAAAAAwADAAAABgAGAAYADAADAAkABgAGAAAAAAAAAAMAAAAAAAAAAwAGAAMABgAAAP3/AwADAAAABgAJAAYADAAMAAkADAAJAAYAAwAAAAMAAwADAAMAAwAGAAMAAAAGAAYACQAGAAMAAAAAAAAAAAAAAAAABgAMAAYAAwADAAMAAwAAAAAABgAGAAMABgAAAAAABgAAAAYAAwADAAAABgAJAAwABgADAAAAAwADAAYABgAJAAYAEwAPAAwABgAGAAMACQAJAAYAAwAPAAYAAwAAAAYAAwAGAAkADwAMAA8ADAAGAAkABgAGAAkABgAPAAYACQAMAAkAAwADAAMAAwAAAAMAAAADAAMABgAGAAYAAwAGAAMAAwADAAYADAAGAAMABgAGAAwACQAMAAYADAAMABMAEwAPAAwACQADAAkACQAJAAwABgAGAAwABgAGAAkACQAJAAMABgAGAAkACQAMAAkADwAWAAwABgAJAAkACQAGAAMADAAJAAkABgAAAAMAAwADAAkABgAGAAMAAwAGAAMACQAJAAMAAwADAAkADAAPAA8ADwAMAAwADAAGAAMABgADAAYABgADAAMAAwADAAMAAwAJAAMACQAJAAYABgADAAAAAAAAAAMAAAADAAMADwAJAAYACQAAAAAAAAAAAAAAAAADAAMAAAAAAAAAAAAAAAMAAwAJAAMAAwAAAAMABgADAAMABgAGAAAAAwAGAAkADAAJAAMAAwAAAAAAAAADAAYAAwAJAAMAAAAAAAAAAwADAAAAAAADAAAAAAAAAAYACQAMAAYABgADAAYABgADAAMABgAGAAYACQAMAAkACQAMAA8ACQAJAAYABgAAAAMAAwAAAAAAAAADAAMAAAAGAAMABgAJAAwADwAMAAwADAAJAAkABgAGAAMAAAAAAAAAAAAAAAMABgADAAkABgAAAAAAAwADAAMAAwAGAAAAAAADAAYABgAJAAYADAAMAAkABgAJAAYAAwADAAMAAwAGAAYACQAGAAwADAADAAMACQAJAAkADAAPAAYACQAGAAwADAAMAAYADAAMAAwACQAGAAYABgADAAYABgADAAMADAAJAAMABgAGAAYACQAJAAkADAAPAAkABgAGAAkACQAGAAAACQADAAYAAwAAAAAABgAGAAkADAAGAAMAAwAGAAMACQADAP3/AAAAAP3/AAAGAAAABgADAAYAAwAAAAAABgADAAYABgADAAMAAwAGAAkADAAMAAMABgAGAAYACQAJAAYAAwAJAAMAAAAGAAMABgAGAAYABgAGAAMABgAJAAYAAwAGAAMAAwADAAMAAwADAAAAAAAAAP3/AwADAAkADwAJAAYACQADAAAAAwAJAAYACQAMAAYACQAGAAYACQAJAAMAAAADAAAAAwAAAAAABgAAAAMAAwADAAAABgAGAAYABgAJAAYABgADAAYADAAGAAMABgAJAAkACQAJAAYABgAJAAYACQAJAAYABgAAAAAAAAAAAAAAAwAGAAYAAwAGAAAAAwADAAAABgAGAAMACQAJAAYABgADAAMAAAADAAMAAAADAAAABgAJAAkABgAGAAMABgAGAAAAAwAJAAMABgAJAAMAAwAJAAMADAAJABMADAATAA8ACQAJAAYABgAGAAkACQAGAAAACQAJAAYADAAMAAkACQAMAAkACQAJAAkADAAJAAMAAwADAAMAAwADAAAAAAAAAAAAAwAAAPr/AAADAAAAAwAAAAMAAwAAAAAAAAAGAAAAAwADAAYABgAGAAMABgAGAAkACQAMAAYAAwADAAAAAwADAAMAAwAGAAMACQAGAAYACQAGAAkACQAJAAMAAwADAAMABgADAAAAAwADAAYACQAGAAYABgAJAAkACQAJAAYACQAJAAkADAAJAAYABgAJAAYAAwAGAAYADAAJAAkACQADAAAABgAGAAkACQAGAAMAAAAAAAAAAAAAAAAA/f8AAP3/AAAAAAMADAAJAAMACQAGAAMAAwAGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAAwADAAAAAwAAAAMAAwAAAAAABgAJAAMAAwADAAAAAAAAAAYACQAJAAYADAAJAAkACQAGAAAAAAADAAAAAwAGAAMADAAMAAwACQAGAAAAAwAAAAAAAAAGAAAACQAJAAYABgAGAAMABgADAAAA/f8AAAAAAAADAAMAAwAAAAkACQAJAAMACQAGAAAAAwAAAP3/AAAGAAMAAwAGAAkACQAGAAAAAwAAAAMAAAAAAAAAAAD9//3/AAAAAAYAAwAGAAAAAAAAAAAAAwADAAYABgAMAAwADAAGAAYABgAAAAAAAwADAAYADAADAAMAAwADAAkABgADAAYABgAGAAAAAAAAAAMABgADAAAAAwAGAAAAAwAAAAMAAwAAAAAAAwADAAMABgAGAAkABgAGAAAABgAAAAMABgAJAAYACQAGAAMABgADAAAAAAADAAAAAwAAAAAAAAD9/wAAAAAAAPr/+v/9//3//f8AAAAABgAGAAMABgAAAAAAAAAAAAAAAwAGAAkABgAJAAYABgADAAAAAwAAAAAAAAAAAAAAAAADAAAAAAAGAAYAAwADAAkAAwAAAP3/AAD9//3/AwAAAAAAAwAAAAMAAAADAAAAAwAAAAAAAwADAAMAAwAAAAAAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAGAAAAAwADAP3/AAD9/wAAAAAAAP3/AAAAAAAAAwADAAAAAwAAAAAAAwAAAAAABgAGAAYAAwADAAAABgADAAYAAwAAAPr//f8AAAAAAAAAAAAAAAAAAAMAAAADAAMA/f8AAP3//f/6//3/AAAAAAAAAwAGAAAAAwAAAAAAAAADAP3/AAD9/wAAAwADAAMACQAMAAYAAAAAAAMAAAADAAAA/f/9/wYAAwAGAAAAAAAAAAAAAAAGAAAABgAGAAAAAwAAAPf/AAD9/wAADAAGAAYACQAAAAkAAwAGAAYAAAAAAAAA+v8AAAAAAAAAAAAABgAAAPf/AAAAAPr/AAD9/wAAAAD9//r/AwADAAAA/f8AAAYAAAADAAAAAAD9/wkADAAPAAkABgADAAAAAAAAAP3/AAAGAAAAAAD6//f/AAD6/wAAAAD9//f/9//6/wAAAAAJAAMAAAAAAAAA/f/9//3/AAADAAYABgADAAMAAAAAAPr//f/3//H/9//9//3/BgADAAMAAAAAAAMAAAADAP3/9//6/wMA+v8AAO3/9//6//3/AAADAP3/BgAGAP3/AwD6/+3//f/9/wAAAwAAAAAA/f/x//r/+v/6/wAA/f8AAAYA+v/0//r/9P/9/wAAAAADAP3/AAADAPr/AAD3//f/+v/9/wAABgAAAAAA/f/6/wAAAAAAAAAA/f8AAP3/AwAAAAAAAwAAAP3/AAADAAYADAAMAAAABgAAAAAA/f8AAAAABgAAAPf//f8AAAAAAAADAAAA9//0/wAA9/8AAPr/9/8AAP3/+v8DAP3/AwAMAP3/BgD6//f/AAAAAAAAAwAAAAAAAAD6/wMA/f/9//3/9//6/wYA/f8AAPr/+v8GAAAAAAADAAMADAAMAP3/AAD3//H/8f/q//H//f/t/wAA9//h/wAA6v/3/wYA/f8GAAwA+v8JAPf/AAAGAAAABgAAAOf/BgAAAP3/DAD6/wAAAAD3/wAABgAGAAAA+v/9/wYABgAMAAMAAAAAAAMA/f8DAAYAAAD9/wAA9//6//3/AAAJAAkAAADt/+r/6v/3//r//f8AAAAAAwAAAAMA+v/9//r//f/3/wYAAAAMAA8ABgAPAA8A9P/6/wAA9P8DAP3//f/6/+3/9//6//T/+v/3/wMAAAD0/wAA/f8GABMABgAAABYAAAAJAAAAAAAAAAYABgAGAAAABgADAAMAAwD6/wAABgADAAAAAAADAAkAAAD9/wAA/f8AAAMA9//0/wMA+v8AAAAA/f/9//3/8f/3//3/AAAMAPr//f/0//H/9P/3//3//f8AAAkAAwAAAAYABgAGABYADAAAAAYAAAADAAYABgAJAA8ACQAMAAYAAwAAAPf/8f/0/+3/7f8DAAAA9P8AAPf/AAD6/wAA9//6/wAACQAGAA8A9/8GAAYA9P/9//r/8f/6//3/AwAAAP3/9P/9//f/9P/9/wAAAAAJAAMAEwAcABkABgAJABMADwAJABMAHAAWABMAAAADAAMABgAMAAYACQADAAkAAAAAAAkAAwAGAAMAAAAAAAAAAAAAAAkA9/8AAAMAAAADAAYACQAPAAYA9//t//H/8f/3//r/CQADAP3/DAADAAMAAwD9/wAA/f/3/+3/8f/3//3/+v8JAAMAAwADAAYAAAAAAAMABgAAAA8ACQAMABwAJgAZACMADwAGAAkAAADx/wMABgAjAAMALwAfADMAGQAAAA8AOQD6/xwAx/+k/wAARgAZADkANgApAPf/LwB0/+T/DwBB/z8AhP+/AEkAJP8=\" type=\"audio/wav\" />\n", "                    Your browser does not support the audio element.\n", "                </audio>\n", "              "], "text/plain": ["<IPython.lib.display.Audio object>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Audio\n", "\n", "audio_bytes = base64.b64decode(pred.audio.data)\n", "array, rate = sf.read(io.BytesIO(audio_bytes), dtype=\"float32\")\n", "Audio(array, rate=rate)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The instruction specifies the target emotion, but is not too informative beyond that. We can also see that the audio score for this sample is not too high. Let's see if we can do better by optimizing this pipeline."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optimize with DSPy\n", "\n", "We can leverage `dspy.MIPROv2` to refine the downstream task objective and produce higher quality TTS instructions, leading to more accurate and expressive audio generations:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_lm = dspy.LM(model='gpt-4o-mini')\n", "\n", "teleprompter = dspy.MIPROv2(metric=audio_similarity_metric, auto=\"light\", prompt_model = prompt_lm)\n", "\n", "optimized_program = teleprompter.compile(EmotionStylePrompter(),trainset=trainset)\n", "\n", "evaluate_program(optimized_program)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's take a look at how the optimized program performs:"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\n", "\n", "\u001b[34m[2025-05-15T22:09:40.088592]\u001b[0m\n", "\n", "\u001b[31mSystem message:\u001b[0m\n", "\n", "Your input fields are:\n", "1. `raw_line` (str)\n", "2. `target_style` (str)\n", "Your output fields are:\n", "1. `reasoning` (str)\n", "2. `openai_instruction` (str)\n", "All interactions will be structured in the following way, with the appropriate values filled in.\n", "\n", "[[ ## raw_line ## ]]\n", "{raw_line}\n", "\n", "[[ ## target_style ## ]]\n", "{target_style}\n", "\n", "[[ ## reasoning ## ]]\n", "{reasoning}\n", "\n", "[[ ## openai_instruction ## ]]\n", "{openai_instruction}\n", "\n", "[[ ## completed ## ]]\n", "In adhering to this structure, your objective is: \n", "        Generate an OpenAI TTS instruction that makes the TTS model speak the given line with the target emotion or style, as if the speaker is a [insert persona relevant to the task, e.g. \"irate customer\", \"angry boss\", etc.]. The instruction should specify the tone, pitch, and other characteristics of the speaker's voice to convey the target emotion.\n", "\n", "\n", "\u001b[31mUser message:\u001b[0m\n", "\n", "[[ ## raw_line ## ]]\n", "It's eleven o'clock\n", "\n", "[[ ## target_style ## ]]\n", "disgust\n", "\n", "Respond with the corresponding output fields, starting with the field `[[ ## reasoning ## ]]`, then `[[ ## openai_instruction ## ]]`, and then ending with the marker for `[[ ## completed ## ]]`.\n", "\n", "\n", "\u001b[31mResponse:\u001b[0m\n", "\n", "\u001b[32m[[ ## reasoning ## ]]\n", "To convey disgust, the speaker's voice should be characterized by a high-pitched tone, a slightly nasal quality, and a sense of revulsion. The speaker's words should be delivered with a sense of distaste and aversion, as if the speaker is trying to convey their strong negative emotions.\n", "\n", "[[ ## openai_instruction ## ]]\n", "Generate a text-to-speech synthesis of the input text \"It's eleven o'clock\" with the following characteristics: \n", "- Tone: Disgusted\n", "- Pitch: High-pitched, slightly nasal\n", "- Emphasis: Emphasize the words to convey a sense of distaste and aversion\n", "- Volume: Moderate to loud, with a sense of rising inflection at the end to convey the speaker's strong negative emotions\n", "- Speaker: A person who is visibly and audibly disgusted, such as a character who has just been served a spoiled meal.\n", "\n", "[[ ## completed ## ]]\u001b[0m\n", "\n", "\n", "\n", "\n", "\n"]}], "source": ["pred = optimized_program(raw_line=testset[1].raw_line, target_style=testset[1].target_style)\n", "\n", "print(audio_similarity_metric(testset[1], pred)) #0.6691027879714966\n", "\n", "dspy.inspect_history(n=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["MIPROv2 Optimized Program Instruction: \n", "```text \n", "Generate an OpenAI TTS instruction that makes the TTS model speak the given line with the target emotion or style, as if the speaker is a [insert persona relevant to the task, e.g. \"irate customer\", \"angry boss\", etc.]. The instruction should specify the tone, pitch, and other characteristics of the speaker's voice to convey the target emotion.\n", "```\n", "\n", "TTS Instruction: \n", "```text\n", "Generate a text-to-speech synthesis of the input text \"It's eleven o'clock\" with the following characteristics: \n", "- Tone: Disgusted\n", "- Pitch: High-pitched, slightly nasal\n", "- Emphasis: Emphasize the words to convey a sense of distaste and aversion\n", "- Volume: Moderate to loud, with a sense of rising inflection at the end to convey the speaker's strong negative emotions\n", "- Speaker: A person who is visibly and audibly disgusted, such as a character who has just been served a spoiled meal.\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "                <audio  controls=\"controls\" >\n", "                    <source src=\"data:audio/wav;base64,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\" type=\"audio/wav\" />\n", "                    Your browser does not support the audio element.\n", "                </audio>\n", "              "], "text/plain": ["<IPython.lib.display.Audio object>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Audio\n", "\n", "audio_bytes = base64.b64decode(pred.audio.data)\n", "array, rate = sf.read(io.BytesIO(audio_bytes), dtype=\"float32\")\n", "Audio(array, rate=rate)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["MIPROv2's instruction tuning added more flavor to the overall task objective, giving more criteria to how the TTS instruction should be defined, and in turn, the generated instruction is much more specific to the various factors of speech prosody and produces a higher similarity score."]}], "metadata": {"kernelspec": {"display_name": "jun2024_py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}