# Real-World Examples

This section demonstrates practical applications of DSPy across different domains and use cases. Each tutorial shows how to build production-ready AI systems using DSPy's modular programming approach.

## Featured Examples

### 📄 [Generating llms.txt](../llms_txt_generation/index.md)
Learn how to create AI-powered documentation generators that analyze codebases and produce structured, LLM-friendly documentation following the llms.txt standard.

**Key Concepts:** Repository analysis, meta-programming, documentation generation

### 📧 [Email Information Extraction](../email_extraction/index.md)
Build intelligent email processing systems that classify messages, extract entities, and identify action items using DSPy's structured prediction capabilities.

**Key Concepts:** Information extraction, classification, text processing

### 🧠 [Memory-Enabled ReAct Agents with Mem0](../mem0_react_agent/index.md)
Create conversational agents with persistent memory using DSPy ReAct and Mem0 integration for context-aware interactions across sessions.

**Key Concepts:** Memory systems, conversational AI, agent persistence

### 💰 [Financial Analysis with Yahoo Finance](../yahoo_finance_react/index.md)
Develop financial analysis agents that fetch real-time market data, analyze news sentiment, and provide investment insights using LangChain tool integration.

**Key Concepts:** Tool integration, financial data, real-time analysis

### 🔄 [Automated Code Generation from Documentation](../sample_code_generation/index.md)
Build a system that automatically fetches documentation from URLs and generates working code examples for any library using DSPy's intelligent analysis.

**Key Concepts:** Web scraping, documentation parsing, automated learning, code generation

### 🎮 [Building a Creative Text-Based AI Game](../ai_text_game/index.md)
Create an interactive text-based adventure game with dynamic storytelling, AI-powered NPCs, and adaptive gameplay using DSPy's modular programming approach.

**Key Concepts:** Interactive storytelling, game state management, character progression, AI-driven narratives
