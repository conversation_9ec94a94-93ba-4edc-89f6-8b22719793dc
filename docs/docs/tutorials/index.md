Welcome to DSPy tutorials! We've organized our tutorials into three main categories to help you get started:

- **Build AI Programs with DSPy**: These hands-on tutorials guide you through building production-ready AI
  applications. From implementing RAG systems to creating intelligent agents, each tutorial demonstrates
  practical use cases. You'll also learn how to leverage DSPy optimizers to enhance your program's performance.

- **Optimize AI Programs with DSPy Optimizers**: These tutorials deep dive into DSPy's optimization capabilities. While
  lighter on programming concepts, they focus on how to systematically improve your AI programs using DSPy
  optimizers, and showcase how DSPy optimizers help improve the quality automatically.

- **DSPy Core Development**: These tutorials cover essential DSPy features and best practices. Learn how to implement
  key functionalities like streaming, caching, deployment, and monitoring in your DSPy applications.


- Build AI Programs with DSPy
    - [Building AI Agents with DSPy](customer_service_agent/index.ipynb)
    - [Building AI Applications by Customizing DSPy Modules](custom_module/index.ipynb)
    - [Retrieval-Augmented Generation (RAG)](rag/index.ipynb)
    - [Building RAG as Agent](agents/index.ipynb)
    - [Entity Extraction](entity_extraction/index.ipynb)
    - [Classification](classification/index.md)
    - [Multi-Hop RAG](multihop_search/index.ipynb)
    - [Privacy-Conscious Delegation](papillon/index.md)
    - [Program Of Thought](program_of_thought/index.ipynb)
    - [Image Generation Prompt iteration](image_generation_prompting/index.ipynb)
    - [Audio](audio/index.ipynb)


- Optimize AI Programs with DSPy
    - [Math Reasoning](math/index.ipynb)
    - [Classification Finetuning](classification_finetuning/index.ipynb)
    - [Advanced Tool Use](tool_use/index.ipynb)
    - [Finetuning Agents](games/index.ipynb)

- Tools, Development, and Deployment
    - [Managing Conversation History](conversation_history/index.md)
    - [Use MCP in DSPy](mcp/index.md)
    - [Output Refinement](output_refinement/best-of-n-and-refine.md)
    - [Saving and Loading](saving/index.md)
    - [Cache](cache/index.md)
    - [Deployment](deployment/index.md)
    - [Debugging & Observability](observability/index.md)
    - [Tracking DSPy Optimizers](optimizer_tracking/index.md)
    - [Streaming](streaming/index.md)
    - [Async](async/index.md)


