# Build AI Programs with DSPy

This section contains hands-on tutorials that guide you through building production-ready AI applications using DSPy. Each tutorial demonstrates practical use cases and shows you how to leverage DSPy's modular programming approach to create robust, maintainable AI systems.

## Core Applications

### [Building AI Agents with DSPy](../customer_service_agent/index.ipynb)
Learn to create intelligent agents that can handle complex customer service scenarios. This tutorial shows how to build agents that can understand context, maintain conversation state, and provide helpful responses.

### [Building AI Applications by Customizing DSPy Modules](../custom_module/index.ipynb)
Discover how to create custom DSPy modules tailored to your specific needs. Learn the patterns for building reusable, composable components that can be shared across different applications.

## Retrieval-Augmented Generation (RAG)

### [Retrieval-Augmented Generation (RAG)](../rag/index.ipynb)
Master the fundamentals of RAG systems with DSPy. Learn how to combine retrieval mechanisms with language models to build systems that can answer questions using external knowledge sources.

### [Building RAG as Agent](../agents/index.ipynb)
Take RAG to the next level by building `ReAct` agent-based systems that can reason about when and how to retrieve information, making your RAG systems more intelligent and adaptive.

### [Multi-Hop RAG](../multihop_search/index.ipynb)
Build sophisticated RAG systems that can perform multi-step reasoning across multiple information sources, perfect for complex research and analysis tasks.

## Specialized Use Cases

### [Entity Extraction](../entity_extraction/index.ipynb)
Learn to build systems that can identify and extract specific entities from text, essential for information processing and data analysis applications.

### [Classification](../classification/index.md)
Build robust text classification systems using DSPy's modular approach with a topic classification example.

### [Privacy-Conscious Delegation](../papillon/index.md)
Explore advanced techniques for building AI systems that respect privacy constraints while maintaining high performance by combining a small local model and an advanced external model.

## Advanced Reasoning

### [Program Of Thought](../program_of_thought/index.ipynb)
Learn to build systems that can generate and execute code to solve complex problems, combining the power of language models with programmatic reasoning.

## Multimodal Applications

### [Image Generation Prompt iteration](../image_generation_prompting/index.ipynb)
Discover how to use DSPy to iteratively improve image generation prompts, creating better visual content through systematic optimization.

### [Audio](../audio/index.ipynb)
Explore audio processing applications with DSPy, learning to build systems that can understand, process, and generate audio content.
