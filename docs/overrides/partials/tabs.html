<!--
  Copyright (c) 2016-2023 <PERSON> <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->

{% import "partials/tabs-item.html" as item with context %}

<!-- Navigation tabs -->
<nav
  class="md-tabs"
  aria-label="{{ lang.t('tabs') }}"
  data-md-component="tabs"
>
  <div class="md-tabs__inner md-grid">

	<!-- Adds tab on right side of header -->
	{% if "FAQ" %}
        <ul class="md-tabs__list" style="float: right;">
            <li class="md-tabs__item">
                <a href="/production/" class="md-tabs__link">
                    DSPy in Production
                </a>
            </li>
	    <li class="md-tabs__item">
                <a href="/community/community-resources/" class="md-tabs__link">
                    Community
                </a>
            </li>
            <li class="md-tabs__item">
                <a href="/faqs/" class="md-tabs__link">
                    FAQ
                </a>
            </li>
        </ul>
	{% endif %}

	<!-- Original tabbed sections -->
	<ul class="md-tabs__list">
	  {% for nav_item in nav %}
		{% if nav_item.title not in ["FAQ", "Community", "DSPy in Production"] %}
            {{ item.render(nav_item) }}
		{% endif %}
	  {% endfor %}
	</ul>
  </div>
</nav>
