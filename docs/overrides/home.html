{% extends "base.html" %}

{% block content %}
<style>
    .md-main__inner .md-grid {
        padding: 0;
        margin: 0;
    }

    .content-container {
        max-width: 100%;
        margin: 0;
        padding: 0;
    }
    
    .hero {
        text-align: center;
        padding: 4rem 2rem;
        margin: 0;
        background-color: #f5f6f77a;
        color: white;
    }
    
    .hero-logo {
        max-width: 15rem;
        height: auto;
        margin: 0 auto;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
        margin: 1.5rem 0;
        color: #e2e8f0;
    }
    
    .cta-button {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        background-color: transparent;
        color: black;
        text-decoration: none;
        border-radius: 0.375rem;
        font-weight: 600;
        border: 2px solid black;
        transition: all 0.3s ease;
    }
    
    .cta-button:hover {
        background-color: white;
        color: black;
        border: 2px solid white;
    }
    
    .features-section {
        padding: 4rem 2rem;
    }
    
    .features-title {
        text-align: center;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 3rem;
        color: #1a202c;
    }
    
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 3rem;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .feature-card {
        text-align: center;
        padding: 1.5rem;
    }
    
    .feature-image {
        width: 10rem;
        height: auto;
        margin: 0 auto 1.5rem;
    }
    
    .feature-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2d3748;
    }
    
    .feature-description {
        color: #4a5568;
        line-height: 1.5;
    }
    
    @media (max-width: 768px) {
        .hero {
            padding: 3rem 1rem;
        }
        
        .hero-logo {
            max-width: 10rem;
        }
        
        .features-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .feature-card {
            padding: 1rem;
        }
    }
</style>

<div class="content-container">
    <div class="hero">
        <img src="{{ 'static/img/dspy_logo.png' | url }}" alt="DSPy Logo" class="hero-logo">
        <p class="hero-subtitle">Programming—not prompting—Language Models</p>
        <a href="{{ 'quick-start/getting-started-1' | url }}" class="cta-button">Get Started with DSPy</a>
    </div>

    <div class="features-section">
        <h2 class="features-title">The Way of DSPy</h2>
        <div class="features-grid">
            <div class="feature-card">
                <img src="{{ 'static/img/optimize.png' | url }}" alt="Systematic Optimization" class="feature-image">
                <h3 class="feature-title">Systematic Optimization</h3>
                <p class="feature-description">Choose from a range of optimizers to enhance your program. Whether it's generating refined instructions, or fine-tuning weights, DSPy's optimizers are engineered to maximize efficiency and effectiveness.</p>
            </div>
            
            <div class="feature-card">
                <img src="{{ 'static/img/modular.png' | url }}" alt="Modular Approach" class="feature-image">
                <h3 class="feature-title">Modular Approach</h3>
                <p class="feature-description">With DSPy, you can build your system using predefined modules, replacing intricate prompting techniques with straightforward, effective solutions.</p>
            </div>
            
            <div class="feature-card">
                <img src="{{ 'static/img/universal_compatibility.png' | url }}" alt="Cross-LM Compatibility" class="feature-image">
                <h3 class="feature-title">Cross-LM Compatibility</h3>
                <p class="feature-description">Whether you're working with powerhouse models like GPT-3.5 or GPT-4, or local models such as T5-base or Llama2-13b, DSPy seamlessly integrates and enhances their performance in your system.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}